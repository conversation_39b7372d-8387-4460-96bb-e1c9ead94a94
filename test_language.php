<?php
// 测试语言包修改
require_once 'thinkphp/base.php';

use think\App;
use think\facade\Lang;
use think\facade\Cookie;

// 模拟应用初始化
App::run();

// 设置不同语言进行测试
$languages = ['zh', 'en-us', 'jp', 'baxi', 'kor', 'thai'];

echo "<h1>语言包修改测试</h1>";
echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>语言</th><th>yqm字段</th><th>Invitation code字段</th></tr>";

foreach($languages as $lang) {
    Lang::range($lang);
    $langFile = "application/index/lang/{$lang}.php";
    
    if(file_exists($langFile)) {
        $langData = include $langFile;
        
        $yqm = isset($langData['yqm']) ? $langData['yqm'] : '未定义';
        $invitationCode = isset($langData['Invitation code']) ? $langData['Invitation code'] : '未定义';
        
        echo "<tr>";
        echo "<td>{$lang}</td>";
        echo "<td>{$yqm}</td>";
        echo "<td>{$invitationCode}</td>";
        echo "</tr>";
    }
}

echo "</table>";

echo "<h2>修改说明</h2>";
echo "<p>1. yqm 字段用于个人中心显示邀请码，已修改为'您的邀请码是'格式</p>";
echo "<p>2. Invitation code 字段用于注册页面输入框placeholder，保持'邀请码'格式</p>";
echo "<p>3. 所有支持的语言包都已相应更新</p>";
?> 