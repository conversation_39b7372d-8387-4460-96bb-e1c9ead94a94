<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
		<link rel="stylesheet" href="../../../p_static1/css/base.css">
		<title>{$Think.lang.Memberupgrade}</title>
		<link rel="stylesheet" href="__ROOT__/public/css/style.css">
		<!--<link rel="stylesheet" href="__ROOT__/public/js/layer_mobile/need/layer.css">-->
		
		<!--JQ-->
		<!--<script src="__ROOT__/static/plugs/jquery/jquery.min.js"></script>-->
		<!--<script type="text/javascript" src="../../../statics/js/swiper.3.1.7.min.js"></script>-->
		<!--<script type="text/javascript" src="../../../statics/js/jquery.SuperSlide.2.1.1.js"></script>-->
		<!--<script type="text/javascript" src="../../../statics/js/TouchSlide.1.0.js"></script>-->
		<!--<script type="text/javascript" src="../../../statics/js/jquery.simplesidebar.js"></script>-->
		
		
		<link rel="stylesheet" href="../../../statics/css/share.css">
		<link rel="stylesheet" href="../../../statics/css/font.css">
		
		<!--JQ-->
		<script type="text/javascript" src="../../../statics/js/jquery-2.1.1.min.js"></script>
		<script type="text/javascript" src="../../../statics/js/jquery-form.js"></script>
		
		<script src="__ROOT__/public/js/layer_mobile/layer.js"></script>
		<link href="__ROOT__/public/js/layer_mobile/need/layer.css?2.0" type="text/css" rel="styleSheet" id="layermcss">
		
		<script type="text/javascript" src="../../../statics/js/swiper.3.1.7.min.js"></script>
		
		
		
		<script src="../../../statics/js/jquery.simplesidebar.js"></script>
		
		<script src="../../../statics/js/jquery.SuperSlide.2.1.1.js"></script>
		
		<script src="../../../statics/js/TouchSlide.1.0.js"></script>
		
		
		
		<script type="text/javascript" src="../../../statics/js/func.js"></script>
		
		
		<script>
		
		    var SITE_URL  = window.location.protocol + '//' + window.location.host;
		
		</script>
		<script>if(("standalone" in window.navigator) && window.navigator.standalone){
		    var noddy, remotes = false;
		    document.addEventListener('click', function(event) {
		        noddy = event.target;
		        while(noddy.nodeName !== "A" && noddy.nodeName !== "HTML") {
		            noddy = noddy.parentNode;
		        }
		        if('href' in noddy && noddy.href.indexOf('http') !== -1 && (noddy.href.indexOf(document.location.host) !== -1 || remotes)){
		            event.preventDefault();
		            document.location.href = noddy.href;
		        }
		    },false);
		}</script>
		
		<style type="text/css">
		    .yhidden{
		        overflow-y:hidden;
		    }
		    .vip_car{
		        width: 100%;
		        height: 6rem;
		        background: -webkit-linear-gradient(#fff, #e2e2e2);
		        background: -o-linear-gradient(#fff, #e2e2e2);
		        background: -moz-linear-gradient(#fff, #e2e2e2);
		        background: linear-gradient(#fff, #ededed);
		        background: -webkit-linear-gradient(left,#27a6fa,#8e71f5);
		        /*border-bottom: 2px red solid;*/
		    }
		    .vip_car_xx{
		        width: 92%;
		        height: 5rem;
		        background: url(../../../public/img/vip_bg.png) no-repeat;
		        background-size: 100% 100%;
		        margin-left: 4%;
		        margin-top: 1rem;
		        float: left;
		        border-radius: 0.5rem 0.5rem 0 0;
		        position: relative;
		
		    }
		    .vip_car_logo{
		        width: 3rem;
		        height: 3rem;
		        border-radius: 3rem;
		        margin-top: 1rem;
		        margin-left: 1rem;
		        float: left;
		    }
		    .vip_car_logo_name{
		        width: 43%;
		        height: 2.5rem;
		        /*background: red;*/
		        float: left;
		        margin-top: 0.5rem;
		        padding-left: 0.5rem;
		        line-height: 2.5rem;
		        font-size: 18px;
		        font-weight: bold;
		        color: #fff;
		
		    }
		    .vip_car_logo_rw{
		        float: left;
		        line-height: 1rem;
		        padding-left: 0.5rem;
		        color: #fff;
		        font-size: 14px;
		        width: 60%;
		        height: 1rem;
		    }
		    .vip_car_hyxq{
		        background: #cfa55d;
		        float: right;
		        height: 1.5rem;
		        line-height: 1.5rem;
		        display: inline-block;
		        position: absolute;
		        top: 1.75rem;
		        right: 0rem;
		        padding: 0 1rem;
		        border-radius: 1.5rem 0 0 1.5rem;
		        color: #997030;
		        font-size: 11px;
		        text-decoration: none;
		    }
		    .vip_car_hyjj{
		        width: 100%;
		        height: 3rem;
		        background: rgba(255,255,255,.25);
		        float: left;
		        margin-top: 0.5rem;
		        padding: 0 1rem;
		        box-sizing: border-box;
		    }
		    .vip_car_hytq{
		        height: 1.5rem;
		        line-height: 1.5rem;
		
		    }
		    .vip_car_hytq span{
		        width: 50%;
		        height: 1.5rem;
		        line-height: 1.5rem;
		        display: inline-block;
		        text-align: center;
		        color: #997030;
		    }
		    .vip_car_yjsl span{
		        text-align: center;
		        /*background: red;*/
		        display: inline-block;
		        width: 50%;
		        font-size: 16px;
		        color: #fff;
		    }
		    .vip_xxjss{
		        width: 92%;
		        height: 6rem;
		        margin: 0.5rem 4% 0;
		    }
		    .vip_xxjss li{
		        width: 33.33%;
		        height: 6rem;
		        float: left;
		    }
		    .vip_tequan{
		        width: 100%;
		        height: 2rem;
		        line-height: 2rem;
		        font-size: 16px;
		        font-weight: bold;
		        padding: 0 0.5rem;
		        box-sizing: border-box;
		        text-align: left;
		        float: left;
		        margin-bottom: 0.25rem;
		        text-align: center;
		        margin: 0.5rem 0;
		
		    }
		    .vip_tequan span{
		        width: 35%;
		        height: 0.02rem;
		        background: #ccc;
		        display: inline-block;
		        /*margin-top: 1rem;*/
		        float: left;
		        margin-top: 0.95rem;
		    }
		    .vip_tequan a{
		        width: 60%;
		        height: 2rem;
		        display: inline-block;
		        line-height: 2.2rem;
		        color: #228aff;
		    }
		    .vip_xxjss li img{
		        width: 3rem;
		        height: 3rem;
		        display: block;
		        margin: 0.25rem auto;
		    }
		    .vip_xxjss li p{
		        width: 100%;
		        height: 2rem;
		        margin-top: -01rem;
		        text-align: center;
		        line-height: 2rem;
		        font-size: 14px;
		        color: #febb35;
		    }
		    .vio_rwktlb{
		        width: 92%;
		        height: 8rem;
		        margin-left: 4%;
		    }
		    .vio_rwktlb li{
		        width: 30.3%;
		        height: 7.5rem;
		        border: 1px #f5f5f5 solid;
		        border-radius: 0.5rem;
		        float: left;
		        box-sizing: border-box;
		        margin-left: 0;
		        /*box-shadow: 0 1px 1px #228aff;*/
		
		    }
		    .vip_hylss{
		        width: 100%;
		        height: 2rem;
		        line-height: 2rem;
		        text-align: center;
		        font-size: 16px;
		        margin-top: 0.5rem;
		        color: #000;
		    }
		    .vip_hyjg{
		        width: 100%;
		        text-align: center;
		        font-size: 12px;
		        font-weight: bold;
		        margin-top: 0rem;
		        color: #228aff;
		    }
		    .vip_hyjg span{
		        font-size: 12px;
		        font-weight: normal;
		        margin-left: 0.25rem;
		    }
		    .vip_yuanjia{
		        width: 100%;
		        height: 1rem;
		        line-height: 1rem;
		        text-align: center;
		        /*text-decoration:line-through;*/
		        font-size: 12px;
		        color:#a6a6a6;
		    }
		    .vio_rwktlb .active{
		        width: 30%;
		        height: 7.5rem;
		        border: 1px #228aff solid;
		        border-radius: 0.5rem;
		        float: left;
		        box-sizing: border-box;
		        /*margin-left: 2.5%;*/
		        background: #228aff;
		    }
		    .vio_rwktlb .active p{
		        color: #fff;
		    }
		    .recharge_box{
		        width: 84%;
		        margin-left: 8%;
		        height: 4rem;
		        padding: 0;
		        margin-bottom: 0;
		        position: static;
		        background: #fff;
		        border: 0;
		    }
		    .vip_lijisj{
		        width: 84%;
		        height: 2.5rem;
		        background: #228aff;
		        margin: 0.5rem 8%;
		        border-radius: 0.5rem;
		        color: #fff;
		    }
		    .vip_yuanjia2{text-align: center;font-size: 12px}
		    .sub .vip_hylss{
		        color: #a29d9d;
		    }
		    .lv .vip_hylss{
		        color: #000;
		    }
		    .avatar{width:20px;height: 20px}
		    .task_box{
		        display: block;
		        background: #eeeeee;
		        border: solid 1px #dddddd;
		        left: 5%;
		        top: 10%;
		        position: fixed;
		        width:90%;margin:0;
		        height:90%;
		    }
		    .task_box .con ul li{padding:1px 10px}
		    .task_box .con ul li a span.zhuangtai{float: none;width:auto;padding-left:10px;padding-right:10px}
		    .task_box .con ul li a{height:30px}
		
		    .recharge_box label{padding:15px 40px;margin:0}
		    .recharge_box label a{position: relative;
		        top:-5px;}
		    .recharge_box label span{position: relative;top:2px}
		
		    .task_box .con ul li{
		        height: auto;
		    }
		</style>
		
		
		
		<style>
			body {
				position: relative;
			}
			/* 头部背景 */
			.p_header-img {
				position: absolute;
				right: 0;
				top: 0;
				width: 11.5rem;
				height: 7.5rem;
				background-repeat: no-repeat;
				background-size: 100% 100%;
				z-index: -99;
			}
			.header-img1 {
				background-image: url(/p_static1/img/vip_bg-1.png);
			}
			.header-img2 {
				background-image: url(/p_static1/img/vip_bg-2.png);
			}
			.header-img3 {
				background-image: url(/p_static1/img/vip_bg-3.png);
			}
			.header-img4 {
				background-image: url(/p_static1/img/vip_bg-4.png);
			}
			.header-img5 {
				background-image: url(/p_static1/img/vip_bg-5.png);
			}
			.header-img6 {
				background-image: url(/p_static1/img/vip_bg-6.png);
			}
			/* 导航栏 */
			.p_nav {
				box-sizing: border-box;
				position: relative;
				display: flex;
				justify-content: center;
				align-items: center;
				height: 3.1rem;
				font-size: 1rem;
				line-height: 1rem;
				font-weight: 600;
				color: rgba(36, 44, 107, 1);
				border-bottom: 0.05rem solid rgba(223, 225, 240, 1);;
			}
			.p_nav-arrow {
				position: absolute;
				left: 0.75rem;
				top: 50%;
				width: 1.65rem;
				height: 1.65rem;
				transform: translate(0, -50%);
			}
			/* 当前个人等级信息 */
			.p_header-info {
				margin: 0.75rem 0.75rem 0;
			}
			.p_header-grade {
				font-size: 0.9rem;
				line-height: 0.9rem;
				font-weight: 600;
			}
			.p_header-text {
				margin-top: 0.5rem;
				font-size: 0.8rem;
				line-height: 0.8rem;
			}
			/* 切换按钮 */
			.p_tabs {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin: 1rem 0.75rem 0;
			}
			.p_tab-item {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 2.2rem;
				height: 1.25rem;
				font-size: 0.8rem;
				line-height: 0.8rem;
				font-weight: 700;
				color: rgba(186, 190, 214, 1);
				background-color: rgba(235, 235, 237, 1);
				border-radius: 0.975rem;
			}
			.active {
				color: rgba(36, 44, 107, 1);
			}
			.active.tab1 {
				background-color: rgba(213, 217, 231, 1);
			}
			.active.tab2 {
				background-color: rgba(184, 208, 234, 1);
			}
			.active.tab3 {
				background-color: rgba(177, 193, 234, 1);
			}
			.active.tab4 {
				background-color: rgba(174, 217, 150, 1);
			}
			.active.tab5 {
				background-color: rgba(140, 215, 170, 1);
			}
			.active.tab6 {
				background-color: rgba(252, 205, 151, 1);
			}
			/* 会员信息 */
			.p_wrapper {
				display: none;
			}
			.p_wrapper.show {
				display: block;
			}
			.p_info {
				position: relative;
				box-sizing: border-box;
				margin: 1rem 0.75rem 0;
				padding: 1.25rem 0.75rem 0;
				height: 9.25rem;
				border-radius: 0.5rem;
				background-repeat: no-repeat;
				background-size: 2.5rem 3.65rem;
				background-position: top 90%;
			}
			.p_info::after {
				display: block;
				content: '';
				position: absolute;
				top: 0;
				right: 1.4rem;
				width: 2.5rem;
				height: 3.65rem;
			}
			#level1 .p_info {
				background: linear-gradient(136.86deg, rgba(230, 233, 242, 1) 0%, rgba(193, 196, 214, 1) 100%);
			}
			#level1 .p_info::after {
				background: url(/p_static1/img/V1.png) no-repeat;
				background-size: 100% 100%;
			}
			#level2 .p_info {
				background: linear-gradient(107.37deg, rgba(213, 229, 247, 1) 0%, rgba(170, 197, 227, 1) 100%);
			}
			#level2 .p_info::after {
				background: url(/p_static1/img/V2.png) no-repeat;
				background-size: 100% 100%;
			}
			#level3 .p_info {
				background: linear-gradient(116.04deg, rgba(209, 221, 255, 1) 0%, rgba(174, 190, 232, 1) 100%);
			}
			#level3 .p_info::after {
				background: url(/p_static1/img/V3.png) no-repeat;
				background-size: 100% 100%;
			}
			#level4 .p_info {
				background: linear-gradient(101.74deg, rgba(221, 237, 211, 1) 0%, rgba(163, 212, 135, 1) 100%);
			}
			#level4 .p_info::after {
				background: url(/p_static1/img/V4.png) no-repeat;
				background-size: 100% 100%;
			}
			#level5 .p_info {
				background: linear-gradient(111.29deg, rgba(186, 245, 205, 1) 0%, rgba(135, 212, 166, 1) 100%);
			}
			#level5 .p_info::after {
				background: url(/p_static1/img/V5.png) no-repeat;
				background-size: 100% 100%;
			}
			#level6 .p_info {
				background: linear-gradient(134.75deg, rgba(255, 232, 207, 1) 0%, rgba(252, 202, 144, 1) 100%);
			}
			#level6 .p_info::after {
				background: url(/p_static1/img/V6.png) no-repeat;
				background-size: 100% 100%;
			}
			.p_info-username {
				font-size: 1rem;
				line-height: 1rem;
				font-weight: 600;
				color: rgba(36, 44, 107, 1);
			}
			.p_level {
				display: flex;
				align-items: center;
				margin-top: 0.475rem;
			}
			.p_level-sign {
				display: flex;
				align-items: center;
				height: 0.75rem;
			}
			#level1 .p_level-sign {
				background: linear-gradient(104.15deg, rgba(225, 228, 237, 1) 0%, rgba(180, 184, 204, 1) 100%);
			}
			#level2 .p_level-sign {
				background: linear-gradient(107.37deg, rgba(213, 229, 247, 1) 0%, rgba(170, 197, 227, 1) 100%);
			}
			#level3 .p_level-sign {
				background: linear-gradient(101.74deg, rgba(207, 219, 255, 1) 0%, rgba(134, 160, 227, 1) 100%);
			}
			#level4 .p_level-sign {
				background: linear-gradient(101.74deg, rgba(221, 237, 211, 1) 0%, rgba(163, 212, 135, 1) 100%);
			}
			#level5 .p_level-sign {
				background: linear-gradient(111.29deg, rgba(186, 245, 205, 1) 0%, rgba(135, 212, 166, 1) 100%);
			}
			#level6 .p_level-sign {
				background: linear-gradient(111.65deg, rgba(255, 232, 207, 1) 0%, rgba(235, 179, 115, 1) 100%);
			}
			.p_level-sign img {
				width: 1.075rem;
				height: 0.75rem;
			}
			.p_level-sign span {
				font-size: 0.5rem;
				line-height: 0.75rem;
				color: rgba(36, 44, 107, 1);
				transform: scale(.85);
			}
			.p_level-progress {
				position: relative;
				margin-left: 1.475rem;
				width: 5.525rem;
				height: 0.1rem;
				background-color: rgba(232, 235, 250, 1);
				border-radius: 0.25rem;
			}
			.p_level-progress-left {
				position: absolute;
				left: 0;
				bottom: 0;
				transform: translate(-50%, 130%);
				font-size: 0.6rem;
				line-height: 0.6rem;
				color: rgba(93, 97, 128, 1);
			}
			.p_level-progress-right {
				position: absolute;
				right: 0;
				bottom: 0;
				transform: translate(20%, 130%);
				font-size: 0.6rem;
				line-height: 0.6rem;
				color: rgba(93, 97, 128, 1);
			}
			.p_des-title-wrapper {
				display: flex;
				align-items: center;
				margin-top: 1.5rem;
			}
			.p_des-title {
				min-width: 40%;
				max-width: 50%;
				font-size: 0.7rem;
				line-height: 0.7rem;
				color: rgba(93, 97, 128, 1);
			}
			.p_des-num-wrapper {
				display: flex;
				align-items: center;
				margin-top: 0.25rem;
			}
			.p_des-num {
				min-width: 40%;
				max-width: 50%;
				font-size: 1rem;
				line-height: 1rem;
				color: rgba(36, 44, 107, 1);
			}
			.p_member {
				position: absolute;
				right: 0;
				bottom: 0;
				padding: 0.35rem 0.4rem;
				font-size: 0.55rem;
				line-height: 0.55rem;
				border-radius: 0.5rem 0 0.5rem 0;
			}
			#level1 .p_member {
				color: rgba(80, 87, 125, 1);
				background-color: rgba(221, 224, 237, 1);
			}
			#level2 .p_member {
				color: rgba(90, 126, 166, 1);
				background-color: rgba(195, 220, 247, 1);
			}
			#level3 .p_member {
				color: rgba(94, 115, 168, 1);
				background-color: rgba(208, 220, 254, 1);
			}
			#level4 .p_member {
				color: rgba(88, 140, 60, 1);
				background-color: rgba(219, 236, 209, 1);
			}
			#level5 .p_member {
				color: rgba(50, 150, 90, 1);
				background-color: rgba(183, 243, 203, 1);
			}
			#level6 .p_member {
				color: rgba(212, 149, 78, 1);
				background-color: rgba(255, 232, 207, 1);
			}
			.p_other {
				margin-top: 1rem;
			}
			.p_other-title {
				position: relative;
				padding-left: 0.75rem;
				font-size: 0.8rem;
				line-height: 0.8rem;
				font-weight: 600;
				color: rgba(36, 44, 107, 1);
			}
			.p_other-title::before {
				display: block;
				content: '';
				position: absolute;
				left: 0;
				top: 0;
				width: 0.15rem;
				height: 0.8rem;
				background-color: rgba(36, 44, 107, 1);
			}
			.p_other-list {
				padding: 0 0.9rem;
			}
			.p_other-item {
				position: relative;
				display: flex;
				flex-direction: column;
				justify-content: center;
				padding: 0.6rem 0 0.6rem 2.3rem;
			}
			.p_other-item-img {
				position: absolute;
				left: 0;
				top: 50%;
				width: 1.75rem;
				height: 2.05rem;
				transform: translate(0, -50%);
			}
			.p_other-item-title {
				font-size: 0.8rem;
				line-height: 0.8rem;
				font-weight: 600;
				color: rgba(36, 44, 107, 1);
			}
			.p_other-item-subtitle {
				margin-top: 0.25rem;
				font-size: 0.55rem;
				line-height: 0.55rem;
				font-weight: 600;
				color: rgba(147, 153, 186, 1);
			}
			/* 按钮 */
			.p_btn-wrapper {
				box-sizing: border-box;
				height: 7.075rem;
				margin-top: 2.5rem;
				padding: 1.125rem 0.75rem 0;
				background-color: #fff;
				border: 0.05rem solid rgba(223, 225, 240, 1);
				border-radius: 0.5rem 0.5rem 0 0;
			}
			.p_btn-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
			}
			.p_btn-text {
				font-size: 0.8rem;
				line-height: 0.8rem;
				font-weight: 600;
				color: rgba(36, 44, 107, 1);
			}
			.p_btn-num {
				font-size: 0.9rem;
				line-height: 0.9rem;
				color: rgba(255, 112, 112, 1);
			}
			.p_btn {
				display: flex;
				justify-content: center;
				align-items: center;
				margin-top: 0.9rem;
				height: 3rem;
				font-size: 0.9rem;
				line-height: 0.9rem;
				font-weight: 600;
				color: rgba(174, 175, 184, 1);
				background-color: rgba(227, 227, 227, 1);
				border-radius: 0.5rem;
			}
			.p_btn.p_active {
				color: rgba(248, 217, 193, 1);
				background-color: rgba(36, 44, 107, 1);
			}</style>
	</head>
	<body>
		<!-- 头部背景 -->
		<div class="p_header-img header-img1" id="header-img"></div>
		
		<!-- 导航栏 -->
		<div class="p_nav">
			<img class="p_nav-arrow" src="/p_static1/img/arrowleft_circle_blue.png" onclick="window.history.go(-1);">
			<div>{$Think.lang.Memberupgrade}</div>
		</div>
		
		<!-- 当前个人等级信息 -->
		<div class="p_header-info">
			<div class="p_header-grade">{$Think.lang.Mygrade}：{$level_name}</div>
			<div class="p_header-text">{$Think.lang.Ordersavailableeveryday}：{$order_num} ({$Think.lang.single})</div>
		</div>
		
		<!-- 切换按钮 -->
		<div class="p_tabs">
			{foreach $member_level as $key=>$vo}
			    <?php $lv = $vo['id'] == $info['level'] ? 1 : ''  ?>
			<div class="tab{$vo['id']} p_tab-item <?php echo $vo['level'] > $info['level'] ? 'can-upgrade' :'' ;?>" data-id="{$vo.level}" data-price="{$vo.num}">lv{$vo.level}</div>
			{/foreach}
		</div>
		
		<?php
		//var_dump($info,$member_level);die;
		
		?>
		
		{foreach $member_level as $key=>$vo}
		    <?php $lv = $vo['id'] == $info['level'] ? 1 : ''  ?>
		<!-- 等级页面 -->
		<div class="p_wrapper" id="level{$vo['id']}">
			<div class="p_info">
				<div class="p_info-username">{:lang('Yitongjin')}</div>
				<div class="p_level">
					<div class="p_level-sign">
						<img src="/p_static1/img/vip_icon-1.png" >
						<span>{:lang('Current level')}</span>
					</div>
					<div class="p_level-progress">
						<div class="p_level-progress-left">{$vo.num * lang('duna')}/{$vo.num * lang('duna')}</div>
						<div class="p_level-progress-right">lv{$vo.level}</div>
					</div>
				</div>
				<div class="p_des-title-wrapper">
					<div class="p_des-title">{:lang('Orders avilable')} <br> {:lang('every day (order)')}</div>
					<div class="p_des-title">{:lang('Commission rate')}</div>
				</div>
				<div class="p_des-num-wrapper">
					<div class="p_des-num">{$vo['order_num']}</div>
					<div class="p_des-num"><?=$vo['bili'] * 100?>%</div>
				</div>
				<div class="p_member">
					<?php if($vo['validity'] != 0){ ?>
					    {$Think.lang.Membervalidityperiod}{$vo.validity}{$Think.lang.day}
					<?php }else{ ?>
					    {$Think.lang.Membervalidityperiod}{$Think.lang.permanent}
					<?php } ?>
				</div>
			</div>
			<div class="p_other">
				<div class="p_other-title">{:lang('Rank equity')}</div>
				<div class="p_other-list">
					<div class="p_other-item">
						<img src="/p_static1/img/vip_wallet-{$vo['id']}.png" class="p_other-item-img">
						<div class="p_other-item-title">{:lang('Commission bonus')}</div>
						<div class="p_other-item-subtitle">{:lang('The higher the grade, the more the Commission')}</div>
					</div>
					<div class="p_other-item">
						<img src="/p_static1/img/vip_task-{$vo['id']}.png" class="p_other-item-img">
						<div class="p_other-item-title">{:lang('More tasks')}</div>
						<div class="p_other-item-subtitle">{:lang('より多くの異なるタスク')}</div>
					</div>
					<div class="p_other-item">
						<img src="/p_static1/img/vip_service-img.png" class="p_other-item-img">
						<div class="p_other-item-title">{:lang('Dedicated')}</div>
						<div class="p_other-item-subtitle">{:lang('More different tasks')}</div>
					</div>
				</div>
			</div>
		</div>
		{/foreach}
		
		<!-- 按钮 -->
		<div class="p_btn-wrapper">
			<div class="p_btn-header">
				<div class="p_btn-text">{:lang('Upgrade needs')}</div>
				<div class="p_btn-num" id="btn-num"></div>
			</div>
			<div class="p_btn" id="submit">{:lang('Upgrade payment now')}</div>
		</div>
		
		<input type="hidden"  id="is_vip_price" value="1">
		<input type="hidden"  id="is_vip_bu" value="1">
		<input type="hidden" name="price" id="price" value="">
		<input type="hidden" name="level" id="level" value="">
		
		<div class="recharge_box2 task_box" style="text-align:center;display:none ;z-index:99999" >
		    <div class="con" style="background: #fff;padding:15px">
		        <ul style="display: block;">
		            <li>
		                <a onclick="javascript:void(0)" class="link">
		                    <p class="clear">
		                        <span class="t">{$Think.lang.SelectPaymentMethod}</span>
		                        <span class="zhuangtai2">
		                                <img id="ico" src="" alt="" style="width: 30px;height: 30px;">
		                            </span>
		                    </p>
		                </a>
		
		                <a onclick="javascript:void(0)" class="link" style="margin-top: 10px">
		                    <p class="clear">
		                        <span class="t">{$Think.lang.Ordernumber}</span>
		                        <span class="zhuangtai" id="orderId">12345678945</span>
		                    </p>
		                </a>
		
		                <a onclick="javascript:void(0)" class="link">
		                    <p class="clear">
		                        <span class="t">{$Think.lang.Rechargeamount}</span>
		                        <span class="zhuangtai2" id="price2">50</span>
		                    </p>
		                </a>
		            </li>
		        </ul>
		
		        <div id="erweima_div" style="display: block;text-align: center;height: 200px;">
		            <img src="" id="erweima" alt="" height="150">
		        </div>
		
		    </div>
		
		
		    <label style="padding:0">*{$Think.lang.Ifdescmanually}</label>
		    <button type="button" class="cancel vip_lijisj">{$Think.lang.cancel}</button>
		    <button type="submit" id="submit_cz" class="vip_lijisj">{$Think.lang.Confirmrecharge}</button>
		    <div id="pay_desc" style="text-align: left">
		
		    </div>
		</div>
		
		<script>
			$('#level1').addClass('show'); // 先显示level1
			$('.tab1').addClass('active'); // 先显示level1的tab
		
		    $(document).ready(function(){
			    // 获取当前用户等级，自动滚动到对应等级
			    var currentLevel = '{$level_name}'; // 获取当前用户的等级
			    console.log("{$Think.lang.Current_user_level}:", currentLevel);
			    
			    if(currentLevel) {
				    // 提取VIP后面的数字
				    var levelNum = currentLevel.replace(/[^0-9]/g, '');
				    console.log("{$Think.lang.Level_number}:", levelNum);
				    
				    if(levelNum) {
					    // 移除默认选中的level1
					    $('#level1').removeClass('show');
					    $('.tab1').removeClass('active');
					    
					    // 根据用户等级自动选中对应的标签
					    $('#header-img').addClass('p_header-img header-img' + levelNum);
					    $('#level' + levelNum).addClass('show');
					    $('.tab' + levelNum).addClass('active');
					    
					    // 如果是可升级按钮，添加active类
					    if($('.tab' + levelNum).hasClass('can-upgrade')) {
						    $('#submit').addClass('p_active');
					    }
					    
					    // 设置价格和等级值
					    var price = $('.tab' + levelNum).attr('data-price');
					    var level = $('.tab' + levelNum).attr('data-id');
					    if(price) $('#price').val(price);
					    if(level) $('#level').val(level);
					    
					    // 更新价格显示
					    var is_vip_bu = $('#is_vip_bu').val();
					    var is_vip_price = $('#is_vip_price').val();
					    if (0 && is_vip_bu == 1) {
						    price = price - is_vip_price;
						    $('#btn-num').html("（需补交 " + price + "）");
					    } else {
						    $('#btn-num').html("（" + price + "）");
					    }
				    }
			    }
			    
		        $('.recharge_box label').click(function(){
		            $('.recharge_box label span').removeClass('active');
		            $(this).find('span').addClass('active');
		            var payment_type = $(this).attr('data-key');
		            $('#payment_type').val(payment_type);
		        });
		
				// 点击切换按钮
		        // $('#vip_sel .sub.lv').click(function(){
		        $('.p_tab-item').click(function(){
					
					// 切换等级页面
					$('#header-img').removeClass();
					$('.p_tab-item').removeClass('active');
					$('.p_wrapper').removeClass('show');
					$('#submit').removeClass('p_active');
					
					$(this).addClass('active');
					
					// 切换页面,包括按钮的样式和数字,还有头部背景图
					let text = $(this).text();
					if (text === 'lv1') {
						$('#header-img').addClass('p_header-img header-img1');
						$('#level1').addClass('show');
					} else if (text === 'lv2') {
						$('#header-img').addClass('p_header-img header-img2');
						$('#level2').addClass('show');
					} else if (text === 'lv3') {
						$('#header-img').addClass('p_header-img header-img3');
						$('#level3').addClass('show');
					} else if (text === 'lv4') {
						$('#header-img').addClass('p_header-img header-img4');
						$('#level4').addClass('show');
					} else if (text === 'lv5') {
						$('#header-img').addClass('p_header-img header-img5');
						$('#level5').addClass('show');
					} else if (text === 'lv6') {
						$('#header-img').addClass('p_header-img header-img6');
						$('#level6').addClass('show');
					}
					
					// 如果可以upgrade就提交按钮填加p_active类
					if ($(this).hasClass('can-upgrade')) {
						$('#submit').addClass('p_active');
					}
					
					
		            var price = $(this).attr('data-price');
		            var level = $(this).attr('data-id');
		            // $(this).addClass('active').siblings().removeClass('active');
		
		            var is_vip_bu = $('#is_vip_bu').val();
		            var is_vip_price = $('#is_vip_price').val();
		            if (0 && is_vip_bu == 1) {
		                price = price - is_vip_price;
		
		                // $('#show_price_1').html("（需补交 ￥" +price+"）");
		                $('#btn-num').html("（需补交 " +price+"）");
		            }else{
		                // $('#show_price_1').html("（￥" +price+"）");
		                $('#btn-num').html("（" +price+"）");
		            }
		
		            $('.recharge_box').show();
		            // $('#submit').show()
		            $('#price').val(price);
		            $('#level').val(level);
		        });
		
		        $('#submit').click(function(){
					if (!$('#submit').hasClass('p_active')) { // 判断是否能提交，升级
						return;
					}
					
		            var payment_type = 999;//$('#payment_type').val();
		            var level = $('#level').val();
		
		            if( level == '' ) {
		                sp_tip('{$Think.lang.Pleasedescraded}.');
		                return false;
		            }
		            if( payment_type == '' ) {
		                sp_tip('{$Think.lang.Pleasedecschannel}.');
		                return false;
		            }
		//            layer.open({
		//                type: 2
		//                ,content: '支付中...'
		//                ,time: 3
		//            });
		            submitPay(payment_type)
		        })
		        //$('#form1').submit();
		
		    });
		
		    function submitPay(paytype) {
		        var payment_type = $('#payment_type').val();
		        var level = $('#level').val();
		
		        if( level == '' ) {
		            sp_tip('{$Think.lang.Pleasedescraded}.');
		            return false;
		        }
		        
		        $('#submit').unbind(); //因为提交太慢，所以先解绑防止重复提交
		
		        $.ajax({
		            type:"POST",
		            url:"{:url('recharge_dovip')}",
		            data :{level:level,type:paytype},
		            dataType:"json",
		            async : false,
		            success:function(coordinates){
		                result = coordinates;
		                if (result.code == 0) {
		                    if (result.info = '{$Think.lang.updatesuccessed}') {
		                        sp_tip(result.info);
		                        setTimeout(function (){
		                           location.reload();
		                        }, 1500);
		
		                        return false;
		                    }
		
		                    if (result.paytype == 'alipay_wap' && result.redirect_url) {
		                        window.location.href = result.redirect_url;
		                        return false;
		                    }
		                    if (result.paytype != '' && result.redirect_url) {
		                        window.location.href = result.redirect_url;
		                        return false;
		                    }
		
		                    if (result.info.name2 == 'card') {
		                        var html = '\
		                    <p class="clear" style="padding: 5px;text-align: left;padding-left: 30px;">\
		                            <span class="t">银行卡账户 : </span>\
		                        <span class="zhuangtai" id="">'+result.info.master_cardnum+'</span>\
		                            </p>\
		                            <p class="clear" style="padding: 5px;text-align: left;padding-left: 30px;">\
		                            <span class="t">收款人　　 : </span>\
		                        <span class="zhuangtai" id="">'+result.info.master_name+'</span>\
		                            </p>\
		                            <p class="clear" style="padding: 5px;text-align: left;padding-left: 30px;">\
		                            <span class="t">所属银行　 : </span>\
		                        <span class="zhuangtai" id="">'+result.info.master_bank+'</span>\
		                            </p>\
		                                ';
		
		
		                        $('#erweima_div').html(html);
		                        //return false;
		                    }
		
		                    $('#ico').attr('src',result.info.ico);
		                    $('#orderId').text(result.info.id);
		                    $('#price2').text(result.info.num);
		                    //$('#pay_desc').html(result.pay_desc);
		                    $('#erweima').attr('src',result.info.ewm);
		                    //$('body').addClass('yhidden');
		                    $('.task_box').show();
		                }else{
		                    sp_tip(result.info);
		                }
		
		
		            },
		            error:function(data){
		                console.log('{$Think.lang.Error}' + data.status)
		            }
		        });
		    }
		    $('.cancel').click(function () {
		        $('.task_box').hide();
		    });
		
		
		    $('#submit_cz').click(function () {
		        layer.open({
		            content: '充值中,请稍后刷新余额!'
		            ,skin: 'msg'
		            ,time: 2 //2秒后自动关闭
		        });
		        $('.task_box').hide();
		    });
		
		    function submit_callback(data){
		        if( data.status == 1 ) {
		            window.location.href = data.url;
		        } else {
		            sp_tip(data.info);
		        }
		    }
		    // //
		    // var swiper = new Swiper('.swiper-container', {
		    //     slidesPerView: 3,
		    //     spaceBetween: 10,
		    //     pagination: {
		    //         el: '.swiper-pagination',
		    //         clickable: true,
		    //     },
		    // });
		
		
		</script>
	</body>
</html>
