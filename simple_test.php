<?php
/**
 * 简单测试页面
 * 直接测试充值记录和团队报告功能
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>简单功能测试</h1>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
.success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
.error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
.warning { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
.info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
.test-btn { 
    display: inline-block; 
    margin: 10px; 
    padding: 15px 20px; 
    background: #007bff; 
    color: white; 
    text-decoration: none; 
    border-radius: 5px;
    font-weight: bold;
}
.test-btn:hover { background: #0056b3; }
pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
</style>";

// 检查用户登录状态
session_start();
$uid_session = $_SESSION['user_id'] ?? null;
$uid_cookie = $_COOKIE['user_id'] ?? null;
$uid = $uid_session ?: $uid_cookie;

echo "<div class='section info'>";
echo "<h2>当前状态</h2>";
echo "<p><strong>Session用户ID:</strong> " . ($uid_session ?: '未设置') . "</p>";
echo "<p><strong>Cookie用户ID:</strong> " . ($uid_cookie ?: '未设置') . "</p>";
echo "<p><strong>最终用户ID:</strong> " . ($uid ?: '未获取到') . "</p>";

if (!$uid) {
    echo "<p class='error'>❌ 用户未登录，请先登录</p>";
    echo "<a href='/index/user/login' class='test-btn'>点击登录</a>";
    echo "</div>";
    exit;
}

echo "<p class='success'>✅ 用户已登录</p>";
echo "</div>";

// 修复建议
echo "<div class='section warning'>";
echo "<h2>🔧 问题分析和修复建议</h2>";

echo "<h3>充值记录显示问题可能原因：</h3>";
echo "<ol>";
echo "<li><strong>用户没有充值记录：</strong> 这是最可能的原因</li>";
echo "<li><strong>数据库字段名不匹配：</strong> 检查xy_recharge表的字段名</li>";
echo "<li><strong>用户ID获取问题：</strong> session或cookie中的用户ID不正确</li>";
echo "<li><strong>权限问题：</strong> 用户没有查看充值记录的权限</li>";
echo "</ol>";

echo "<h3>团队报告错误的原因：</h3>";
echo "<ol>";
echo "<li><strong>字段名错误：</strong> 使用了'pid'而不是'parent_id'（已修复）</li>";
echo "<li><strong>模型文件缺失：</strong> admin/Users模型可能不存在</li>";
echo "<li><strong>数据库连接问题：</strong> 查询时出现异常</li>";
echo "</ol>";
echo "</div>";

echo "<div class='section success'>";
echo "<h2>✅ 已实施的修复</h2>";
echo "<ul>";
echo "<li>✅ 修复了团队报告中的字段名错误（pid → parent_id）</li>";
echo "<li>✅ 移除了不存在的_query方法调用</li>";
echo "<li>✅ 修复了充值记录的数据处理逻辑</li>";
echo "<li>✅ 添加了详细的错误日志记录</li>";
echo "<li>✅ 改进了用户认证逻辑</li>";
echo "</ul>";
echo "</div>";

echo "<div class='section info'>";
echo "<h2>🧪 测试步骤</h2>";
echo "<p>请按以下顺序进行测试：</p>";
echo "<ol>";
echo "<li>确保已登录系统</li>";
echo "<li>点击下方测试链接</li>";
echo "<li>如果仍有问题，查看浏览器控制台和服务器日志</li>";
echo "</ol>";

echo "<div style='text-align: center; margin: 20px 0;'>";
echo "<a href='/index/ctrl/recharge_admin' class='test-btn' target='_blank'>📊 测试充值记录</a>";
echo "<a href='/index/ctrl/junior' class='test-btn' target='_blank'>👥 测试团队报告</a>";
echo "<a href='/index/ctrl/deposit_admin' class='test-btn' target='_blank'>💰 测试提现记录</a>";
echo "</div>";
echo "</div>";

echo "<div class='section warning'>";
echo "<h2>📋 如果问题仍然存在</h2>";

echo "<h3>充值记录问题排查：</h3>";
echo "<ol>";
echo "<li>检查数据库中是否有充值记录：<code>SELECT * FROM xy_recharge WHERE uid = {$uid}</code></li>";
echo "<li>检查用户ID是否正确：<code>SELECT * FROM xy_users WHERE id = {$uid}</code></li>";
echo "<li>查看服务器日志文件：<code>runtime/log/</code></li>";
echo "<li>清除浏览器缓存和ThinkPHP缓存</li>";
echo "</ol>";

echo "<h3>团队报告问题排查：</h3>";
echo "<ol>";
echo "<li>检查xy_users表结构：<code>SHOW COLUMNS FROM xy_users</code></li>";
echo "<li>确认parent_id字段存在</li>";
echo "<li>检查admin/Users模型文件是否存在</li>";
echo "<li>查看具体的SQL错误信息</li>";
echo "</ol>";
echo "</div>";

echo "<div class='section info'>";
echo "<h2>📞 技术支持信息</h2>";
echo "<p>如果问题持续存在，请提供以下信息：</p>";
echo "<ul>";
echo "<li>当前用户ID: <strong>{$uid}</strong></li>";
echo "<li>浏览器控制台的错误信息</li>";
echo "<li>服务器错误日志内容</li>";
echo "<li>数据库表结构信息</li>";
echo "</ul>";

echo "<h3>快速诊断命令：</h3>";
echo "<pre>";
echo "-- 检查充值记录\n";
echo "SELECT COUNT(*) as total_records FROM xy_recharge;\n";
echo "SELECT COUNT(*) as user_records FROM xy_recharge WHERE uid = {$uid};\n\n";
echo "-- 检查用户表结构\n";
echo "SHOW COLUMNS FROM xy_users;\n\n";
echo "-- 检查团队关系\n";
echo "SELECT COUNT(*) as children FROM xy_users WHERE parent_id = {$uid};";
echo "</pre>";
echo "</div>";

echo "<div class='section success'>";
echo "<h2>🎯 预期结果</h2>";
echo "<p>修复后应该实现：</p>";
echo "<ul>";
echo "<li>✅ 充值记录页面正常显示（如果有记录）或显示"没有记录"</li>";
echo "<li>✅ 团队报告页面正常显示团队统计信息</li>";
echo "<li>✅ 页面不再跳转到首页</li>";
echo "<li>✅ 不再出现SQL错误</li>";
echo "</ul>";
echo "</div>";
?>
