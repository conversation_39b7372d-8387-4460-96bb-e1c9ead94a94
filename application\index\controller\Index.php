<?php

// +----------------------------------------------------------------------
// | ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2019
// +----------------------------------------------------------------------

// +----------------------------------------------------------------------

// +----------------------------------------------------------------------
// |

// +----------------------------------------------------------------------

namespace app\index\controller;

use library\Controller;
use think\Db;

/**
 * 应用入口
 * Class Index
 * @package app\index\controller
 */
class Index extends Base
{
    public function lang(){

         $lang=input('type');
          switch ($lang) {
              case 'en-ww':
                  cookie('think_var', 'en-ww');
              break;
              case 'zh-cn':
                  cookie('think_var', 'zh-cn');
              break;
              case 'en-in':
                 cookie('think_var','en-in');
              break;
          }

    }

    public function d()
    {
        echo date('y-m-d H:i:s',time());
    }

    /**
     * 入口跳转链接
     */
    public function index()
    {
        // 保留当前语言设置
        $current_lang = cookie('lang');
        if($current_lang == 'arabic') {
            cookie('lang', 'arabic', time()+3600*24*30); // 30天有效期
        }
        
        $this->redirect('home');
    }

    public function home()
    {
        // 安全修复：使用session而不是cookie获取用户ID
        $uid = session('user_id');
        
        $this->users = db('xy_users')->field('username,tel,level,id,headpic,balance,freeze_balance,lixibao_balance,invite_code,show_td')->find($uid);
        $this->info = Db::name('xy_index_msg')->field('content')->select();
        // dump($this->info[9]);die;
        $this->balance = Db::name('xy_users')->where('id',$uid)->sum('balance');
        if(cookie('think_var')=="zh-cn"){
             $this->banner = Db::name('xy_banner')->where('type',1)->select();
        }elseif(cookie('lang')=="en-us"){
             $this->banner = Db::name('xy_banner')->where('type',2)->select();
        }elseif(cookie('lang')=='th-th'){
            $this->banner = Db::name('xy_banner')->where('type',3)->select();
        }elseif(cookie('lang')=='arabic'){
            // 检查是否有阿拉伯语的banner
            $arabicBanners = Db::name('xy_banner')->where('type',4)->select();
            if(!empty($arabicBanners)){
                $this->banner = $arabicBanners;
            } else {
                // 如果没有阿拉伯语banner，使用英语版本
                $this->banner = Db::name('xy_banner')->where('type',2)->select();
                // 记录错误日志
                \think\facade\Log::write('缺少阿拉伯语banner，使用英语版本替代', 'error');
            }
        }else{
            $this->banner = Db::name('xy_banner')->where('type',1)->select();
        }


         $level = db('xy_users')->where('id',$uid)->field('level')->find();
         // 安全处理level值，确保为数字
         $levelValue = isset($level['level']) && is_numeric($level['level']) ? (int)$level['level'] : 0;
         $this->assign('level', $levelValue + 1);
        //if($this->banner) $this->banner = explode('|',$this->banner);
        $this->notice = db('xy_index_msg')->where('id',1)->value('content');
        $this->hezuo = db('xy_index_msg')->where('id',4)->value('content');;
        $this->jianjie = db('xy_index_msg')->where('id',2)->value('content');;
        $this->guize = db('xy_index_msg')->where('id',3)->value('content');;;

        $gundong=db('xy_index_msg')->where('id',8)->find();
         $this->gundong=$gundong['content'];
        if(cookie('lang')=="zh-cn"){
            $this->gundong=$gundong['content'];
        }elseif(cookie('lang')=="en-us"){
             $this->gundong=$gundong['e'];
        }elseif(cookie('lang')=='th-th'){
             $this->gundong=$gundong['t'];
        }elseif(cookie('lang')=='arabic'){
            // 使用安全访问方法
            $this->gundong = $this->safeGet($gundong, 'arabic', 
                $this->safeGet($gundong, 'en-us', 
                    $this->safeGet($gundong, 'e', 
                        $this->safeGet($gundong, 'content', '')
                    )
                )
            );
        }

        $tanchunag=db('xy_index_msg')->where('id',11)->find();

        // $this->tanchunag = $tanchunag['e'];

       if(cookie('lang')=="en-us"){

            $this->tanchunag=$tanchunag['e'];
        }elseif(cookie('lang')=="baxi"){

            $this->tanchunag=$tanchunag['baxi'];
        }elseif(cookie('lang')=="moxige"){

            $this->tanchunag=$tanchunag['moxige'];
        }elseif(cookie('lang')=="tuerqi"){

            $this->tanchunag=$tanchunag['tuerqi'];
        }elseif(cookie('lang')=="arabic"){
            // 使用安全访问方法
            $this->tanchunag = $this->safeGet($tanchunag, 'arabic', 
                $this->safeGet($tanchunag, 'en-us', 
                    $this->safeGet($tanchunag, 'e', 
                        $this->safeGet($tanchunag, config('app.default_lang'), '')
                    )
                )
            );
        }else{
            // 如果默认语言是en-us，使用e字段
            if(config('app.default_lang') == 'en-us'){
                $this->tanchunag=$tanchunag['e'];
            } else {
                $this->tanchunag=$tanchunag[config('app.default_lang')];
            }
        }


        // if(cookie('lang')=="zh"){
        //     $this->tanchunag = $tanchunag['content'];
        // }elseif(cookie('lang')=="en"){
        //     $this->tanchunag = $tanchunag['en-us'];


        // }elseif(cookie('lang')=='jp'){
        //     $this->tanchunag = $tanchunag['jp'];
        // }elseif(cookie('lang')=='kor'){
        //     $this->tanchunag = $tanchunag['kor'];
        // }elseif(cookie('lang')=='cht'){
        //     $this->tanchunag = $tanchunag['cht'];
        // }elseif(cookie('lang')=='spa'){
        //     $this->tanchunag = $tanchunag['spa'];
        // }


        $dev = new \org\Mobile();
        $t = $dev->isMobile();
        if (!$t) {
            if (config('app_only')) {
                header('Location:/app');
            }
        }



        //var_dump($this->banner);die;
        //model('admin/Users')->create_qrcode('',);
        $list = db('xy_convey')
            ->alias('xc')
            ->leftJoin('xy_users u','u.id=xc.uid')
            ->field('xc.*,u.username,u.tel')
            ->where('xc.status',1)
            ->limit(15)
            ->order('xc.id desc')
           ->select();

        //var_dump($list);die;


        $list2 = [
            ['tel' => '139456123698', 'num' =>  23.98, 'addtime' =>  time() - rand(1000,999999)],
            ['tel' => '173456129020', 'num' =>  103.02, 'addtime' =>  time() - rand(1000,999999)],
            ['tel' => '131551220000', 'num' =>  3.00, 'addtime' =>  time() - rand(1000,999999)],
            ['tel' => '181456125024', 'num' =>  9.5, 'addtime' =>  time() - rand(1000,999999)],
            ['tel' => '138852362105', 'num' =>  19.05, 'addtime' =>  time() - rand(1000,999999)],
        ];
        if (count($list) < 5 ) {
            $list = array_merge($list,$list2);
        }

        if ($list) {
            foreach ($list as &$item) {
                $item['tel'] = substr_replace($item['tel'], '****', 3, 4);
                $item['num'] =lang('获得返佣').$item['num'] ;
                $item['addtime'] = date('m-d H:i', $item['addtime']); ;
            }
        }

        $this->list = $list;

        $this->assign('pic','/upload/qrcode/user/'.($uid%20).'/'.$uid.'-1.png');
        
        // 获取原始任务分类数据，包含task_enabled字段
        $original_cate = db('xy_goods_cate')->alias('c')
            ->leftJoin('xy_level u','u.id=c.level_id')
            ->field('c.name,c.id,c.cate_info,c.cate_pic,c.pipei_min,c.pipei_max,c.bili,u.name as levelname,u.pic,u.pic2,u.level,u.num_min,u.order_num,u.task_enabled')
            ->order('c.id asc')->select();

        // 获取用户信息
        $user_info = db('xy_users')->find($uid);
        $user_level = $user_info ? $user_info['level'] : 1;

        // 处理任务分类数据，添加状态标识
        $processed_cate = [];
        foreach ($original_cate as $cate) {
            // 添加任务开关状态标识
            $cate['is_task_enabled'] = isset($cate['task_enabled']) ? (int)$cate['task_enabled'] : 1;
            
            // 添加用户是否可访问标识（用户等级 >= 任务等级）
            $cate['user_can_access'] = $user_level >= $cate['level'];
            
            // 添加最终状态标识（既要用户等级够，又要任务开关开启）
            $cate['is_available'] = $cate['user_can_access'] && $cate['is_task_enabled'];
            
            $processed_cate[] = $cate;
        }

        $this->cate = $processed_cate;

        //一天的
        $this->lixibao = db('xy_lixibao_list')->order('id asc')->find();

        //
        $yes1 = strtotime( date("Y-m-d 00:00:00",strtotime("-1 day")) );
        $yes2 = strtotime( date("Y-m-d 23:59:59",strtotime("-1 day")) );
        // $this->tod_user_yongjin = db('xy_convey')->where('uid',$uid)->where('status',1)->where('addtime','between',[strtotime('Y-m-d 00:00:00'),time()])->sum('commission');
        $this->tod_user_yongjin = db('xy_balance_log')->where('uid',$uid)->where('status',1)->where('type','in',[3, 6])->where('addtime','between',[strtotime(date('Y-m-d'). '00:00:00'),time()])->sum('num');
        $this->yes_user_yongjin = db('xy_convey')->where('uid',$uid)->where('status',1)->where('addtime','between',[$yes1,$yes2])->sum('commission');
        // $this->user_yongjin = db('xy_convey')->where('uid',$uid)->where('status',1)->sum('commission');
        $this->user_yongjin = db('xy_balance_log')->where('uid',$uid)->where('status',1)->where('type','in',[3, 6])->sum('num');

        $this->info = db('xy_users')->find($uid);
        $this->news = db('xy_index_msg')->where("type", 10)->select();


        $this->xy_message2 = db('xy_message2')->select();

        return $this->fetch();
    }

    //获取首页图文
    public function get_msg()
    {
        $type = input('post.type/d',1);
        $data = Db::name('xy_index_msg')->find($type);
        if($data)
            return json(['code'=>0,'info'=>lang('请求成功'),'data'=>$data]);
        else
            return json(['code'=>1,'info'=>lang('暂无数据')]);
    }




    //获取首页图文
    public function getTongji()
    {
        $type = input('post.type/d',1);
        $data = array();

        $data['user'] = db('xy_users')->where('status',1)->where('addtime','between',[strtotime(date('Y-m-d'))-24*3600,time()])->count('id');
        $data['goods'] = db('xy_goods_list')->count('id');;
        $data['price'] = db('xy_convey')->where('status',1)->where('endtime','between',[strtotime(date('Y-m-d'))-24*3600,strtotime(date('Y-m-d'))])->sum('num');
        $user_order = db('xy_convey')->where('status',1)->where('addtime','between',[strtotime(date('Y-m-d')),time()])->field('uid')->Distinct(true)->select();
        $data['num'] = count($user_order);

        if($data){
            return json(['code'=>0,'info'=>lang('请求成功'),'data'=>$data]);
        } else {
            return json(['code' => 1, 'info' => lang('暂无数据')]);
        }
    }




    function getDanmu()
    {
        $barrages=    //弹幕内容
            array(
                array(
                    'info'   => '用户173***4985开通会员成功',
                    'href'   => '',

                ),
                array(
                    'info'   => '用户136***1524开通会员成功',
                    'href'   => '',
                    'color'  =>  '#ff6600'

                ),
                array(
                    'info'   => '用户139***7878开通会员成功',
                    'href'   => '',
                    'bottom' => 450 ,
                ),
                array(
                    'info'   => '用户159***7888开通会员成功',
                    'href'   => '',
                    'close'  =>false,

                ),array(
                'info'   => '用户151***7799开通会员成功',
                'href'   => '',

                )
            );

        echo   json_encode($barrages);
    }

    //获取可用的VIP等级
    public function getAvailableVips()
    {
        $uid = session('user_id');
        if (!$uid) {
            return json(['code' => 1, 'info' => '用户未登录']);
        }

        // 获取原始任务分类数据，包含task_enabled字段
        $original_cate = db('xy_goods_cate')->alias('c')
            ->leftJoin('xy_level u','u.id=c.level_id')
            ->field('c.name,c.id,c.cate_info,c.cate_pic,c.pipei_min,c.pipei_max,c.bili,u.name as levelname,u.pic,u.pic2,u.level,u.num_min,u.order_num,u.task_enabled')
            ->order('c.id asc')->select();

        // 获取用户信息
        $user_info = db('xy_users')->find($uid);
        $user_level = $user_info ? $user_info['level'] : 1;

        // 处理任务分类数据，只返回可用的VIP等级
        $available_vips = [];
        foreach ($original_cate as $cate) {
            // 添加任务开关状态标识
            $cate['is_task_enabled'] = isset($cate['task_enabled']) ? (int)$cate['task_enabled'] : 1;
            
            // 添加用户是否可访问标识（用户等级 >= 任务等级）
            $cate['user_can_access'] = $user_level >= $cate['level'];
            
            // 添加最终状态标识（既要用户等级够，又要任务开关开启）
            $cate['is_available'] = $cate['user_can_access'] && $cate['is_task_enabled'];
            
            if ($cate['is_available']) {
                $available_vips[] = [
                    'id' => $cate['id'],
                    'level' => $cate['level'],
                    'name' => $cate['name']
                ];
            }
        }

        return json(['code' => 0, 'info' => '获取成功', 'data' => $available_vips]);
    }

    //智能VIP跳转
    public function getSmartVipRedirect()
    {
        $target_vip = input('post.target_vip/d', 0);
        $uid = session('user_id');
        
        if (!$uid) {
            return json(['code' => 1, 'info' => '用户未登录']);
        }

        if (!$target_vip) {
            return json(['code' => 1, 'info' => '参数错误']);
        }

        // 获取可用VIP等级数据
        $vip_result = $this->getAvailableVips();
        $vip_data = json_decode($vip_result->getData(), true);
        
        if ($vip_data['code'] != 0) {
            return json(['code' => 1, 'info' => '获取VIP数据失败']);
        }

        $available_vips = $vip_data['data'];
        
        // 检查目标VIP是否可用
        $target_available = false;
        foreach ($available_vips as $vip) {
            if ($vip['id'] == $target_vip) {
                $target_available = true;
                break;
            }
        }

        if ($target_available) {
            // 目标VIP可用，直接返回
            return json([
                'code' => 0, 
                'info' => '目标VIP可用', 
                'data' => [
                    'redirect_vip' => $target_vip,
                    'is_redirect' => false
                ]
            ]);
        }

        // 目标VIP不可用，寻找最佳替代方案（向下兼容）
        $best_vip = null;
        $best_level = 0;
        
        foreach ($available_vips as $vip) {
            if ($vip['id'] <= $target_vip && $vip['level'] > $best_level) {
                $best_vip = $vip;
                $best_level = $vip['level'];
            }
        }

        if ($best_vip) {
            return json([
                'code' => 0, 
                'info' => '找到替代VIP', 
                'data' => [
                    'redirect_vip' => $best_vip['id'],
                    'is_redirect' => true,
                    'original_vip' => $target_vip,
                    'message' => "您选择的VIP{$target_vip}暂时关闭，为您跳转到VIP{$best_vip['id']}"
                ]
            ]);
        }

        return json(['code' => 1, 'info' => '暂无可用的VIP等级']);
    }

}
