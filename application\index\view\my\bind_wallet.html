<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
		<link rel="stylesheet" href="/p_static1/css/base.css">
		<title>{:lang('绑定钱包地址')}</title>
		
		<link href="/static_new6/css/app.7b22fa66c2af28f12bf32977d4b82694.css" rel="stylesheet">
		<link rel="stylesheet" href="/static_new/css/public.css">
		
		<script charset="utf-8" src="/static_new/js/jquery.min.js"></script>
		<script charset="utf-8" src="/static_new/js/dialog.min.js"></script>
		<script charset="utf-8" src="/static_new/js/common.js"></script>
		
		<style type="text/css" title="fading circle style">
		    .circle-color-8 > div::before {
		        background-color: #ccc;
		    }
		</style>
		
		<style>
			body {
				padding-top: 3.1rem;
				background-color: rgba(245, 245, 247, 1);
			}
			/* 导航栏 */
			.p_nav {
				box-sizing: border-box;
				position: fixed;
				top: 0;
				left: 0;
				display: flex;
				justify-content: center;
				align-items: center;
				width: 100%;
				height: 3.1rem;
				font-size: 1rem;
				line-height: 1rem;
				font-weight: 600;
				color: rgba(36, 44, 107, 1);
				background-color: #fff;
				border-bottom: 0.05rem solid rgba(207, 209, 230, 1);
				z-index: 99;
			}
			.p_nav-arrow {
				position: absolute;
				left: 0.75rem;
				top: 50%;
				width: 1.65rem;
				height: 1.65rem;
				transform: translate(0, -50%);
			}
			/* 填写信息 */
			.p_form {
				margin: 1rem 0.75rem 0;
			}
			.p_form-title {
				font-size: 0.7rem;
				line-height: 0.7rem;
				font-weight: 600;
				color: rgba(36, 44, 107, 1);
			}
			.p_form-item-wrapper {
				margin-top: 0.75rem;
				padding: 0 0.75rem;
				background-color: rgba(255, 255, 255, 1);
				border-radius: 0.5rem;
			}
			.p_form-item {
				display: flex;
				align-items: center;
				height: 3.25rem;
			}
			.p_form-item-label {
				flex: 0 0 6.3rem;
				font-size: 0.7rem;
				line-height: 0.7rem;
				font-weight: 600;
				color: rgba(119, 123, 158, 1);
			}
			.p_form-item-input {
				width: 100%;
				padding-bottom: 0.3rem;
				font-size: 0.9rem;
				line-height: 0.9rem;
				font-weight: 600;
				color: rgba(36, 44, 107, 1);
				border-bottom: 0.05rem solid rgba(207, 209, 230, 1);
			}
			.p_form-item-input::-webkit-input-placeholder {
				font-size: 0.6rem;
				line-height: 0.6rem;
				font-weight: normal;
			}
			.p_form-item-select {
				width: 100%;
				font-size: 0.9rem;
				line-height: 0.9rem;
				font-weight: 600;
				color: rgba(36, 44, 107, 1);
			}
			/* 提示 */
			.p_tips {
				margin: 0.75rem 0.75rem 0;
				font-size: 0.6rem;
				line-height: 0.8rem;
				color: rgba(255, 112, 112, 1);
			}
			.p_tips-img {
				width: 0.65rem;
				height: 0.575rem;
			}
			/* 按钮 */
			.p_btn {
				display: flex;
				justify-content: center;
				align-items: center;
				margin: 1.35rem 0.75rem 1.1rem;
				height: 2.2rem;
				font-size: 0.9rem;
				line-height: 0.9rem;
				font-weight: 600;
				color: rgba(248, 217, 193, 1);
				background-color: rgba(36, 44, 107, 1);
				border-radius: 0.5rem;
			}
			
			/* 已绑定钱包信息样式 */
			.p_wallet-info {
				margin: 1rem 0.75rem 0;
			}
			.p_wallet-info-title {
				font-size: 0.7rem;
				line-height: 0.7rem;
				font-weight: 600;
				color: rgba(36, 44, 107, 1);
				margin-bottom: 0.75rem;
			}
			.p_wallet-card {
				background-color: rgba(255, 255, 255, 1);
				border-radius: 0.5rem;
				padding: 0.75rem;
				border: 0.05rem solid rgba(207, 209, 230, 1);
			}
			.p_wallet-item {
				display: flex;
				align-items: center;
				margin-bottom: 0.5rem;
			}
			.p_wallet-item:last-child {
				margin-bottom: 0;
			}
			.p_wallet-item-label {
				flex: 0 0 6rem;
				font-size: 0.65rem;
				line-height: 0.65rem;
				font-weight: 600;
				color: rgba(119, 123, 158, 1);
			}
			.p_wallet-item-value {
				flex: 1;
				font-size: 0.75rem;
				line-height: 0.75rem;
				font-weight: 600;
				color: rgba(36, 44, 107, 1);
				word-break: break-all;
			}
			.p_wallet-status {
				display: inline-block;
				padding: 0.1rem 0.3rem;
				font-size: 0.55rem;
				line-height: 0.55rem;
				background-color: rgba(76, 175, 80, 0.1);
				color: rgba(76, 175, 80, 1);
				border-radius: 0.2rem;
				margin-left: 0.3rem;
			}
		</style>
	</head>
	<body>
		<!-- 导航栏 -->
		<div class="p_nav">
			<img src="/p_static1/img/arrowleft_circle_blue.png" class="p_nav-arrow" onclick="window.history.go(-1);">
			<div>{:lang('我的钱包')}</div>
		</div>
		
		<!-- 已绑定钱包信息显示 -->
		<?php if (!empty($info['cardnum'])): ?>
		<div class="p_wallet-info">
			<div class="p_wallet-info-title">{:lang('当前绑定钱包信息')}</div>
			<div class="p_wallet-card">
				<div class="p_wallet-item">
					<div class="p_wallet-item-label">{:lang('持有人姓名')}：</div>
					<div class="p_wallet-item-value"><?php echo htmlspecialchars($info['username']); ?></div>
				</div>
				<div class="p_wallet-item">
					<div class="p_wallet-item-label">{:lang('钱包地址')}：</div>
					<div class="p_wallet-item-value"><?php echo htmlspecialchars($info['cardnum']); ?></div>
				</div>
				<div class="p_wallet-item">
					<div class="p_wallet-item-label">{:lang('钱包类型')}：</div>
					<div class="p_wallet-item-value">
						<?php echo !empty($info['wallet_type']) ? htmlspecialchars($info['wallet_type']) : lang('未设置'); ?>
						<span class="p_wallet-status">{:lang('已绑定')}</span>
					</div>
				</div>
			</div>
		</div>
		<?php endif; ?>
		
		<form action="" id="login-form">
			<!-- 填写信息 -->
			<div class="p_form">
				<div class="p_form-title">
					<?php if (!empty($info['cardnum'])): ?>
						{:lang('修改钱包信息')}
					<?php else: ?>
						{:lang('Youridentity')}
					<?php endif; ?>
				</div>
				<div class="p_form-item-wrapper">
					<div class="p_form-item">
						<label class="p_form-item-label">{:lang('actualname')}</label>
						<input class="p_form-item-input" type="text" name="username" value="{$info.username}" placeholder="{:lang('请输入您的真实姓名')}">
					</div>
					<div class="p_form-item">
						<label class="p_form-item-label">{:lang('钱包地址')}</label>
						<input class="p_form-item-input" type="text" name="card_bak" maxlength="64" value="{$cardnum_bak}" placeholder="{:lang('请输入钱包地址')}" <?php if (!empty($info['cardnum'])) { echo ''; } ?>>
						<input data-v-8831f782="" type="hidden" name="card" class="J_cardnum" value="{$info.cardnum}">
					</div>
					<div class="p_form-item">
						<label class="p_form-item-label">{:lang('钱包类型')}</label>
						<select class="p_form-item-select" name="wallet_type">
							<option value="" selected>{:lang('请选择钱包类型')}</option>
							<option value="TRC20">{:lang('TRC20')}</option>
							<option value="ERC20">{:lang('ERC20')}</option>
							<option value="BNB">{:lang('BNB')}</option>
							<option value="BTC">{:lang('BTC')}</option>
						</select>
					</div>
					<div class="p_form-item">
						<label class="p_form-item-label">{:lang('Fundpassword')}</label>
						<input class="p_form-item-input" type="password" name="old_pwd" placeholder="{:lang('money_password')}">
					</div>
				</div>
			</div>
			
			<div class="p_form">
				<div class="p_form-title">{:lang('Moreinformation')}</div>
			</div>
			
			<!-- 提示 -->
			<div class="p_tips">
				<img class="p_tips-img" src="/p_static1/img/warning.svg" >
				<span>{:lang('钱包地址说明')}</span>
			</div>
			
			<!-- 按钮 -->
			<div class="p_btn save-btn">{:lang('Settinginformation')}</div>
		</form>
		<script type="application/javascript">
		
		    $(function () {
		        
		        var wallet_type = "{$info.wallet_type}";
		        if (wallet_type != '') {
		            $("select[name=wallet_type]").val(wallet_type);
		        }
		        
		        // 检查是否已绑定钱包，调整按钮文字
		        var hasWallet = "{$info.cardnum}";
		        if (hasWallet) {
		            $(".save-btn").text("{:lang('更新钱包信息')}");
		        }
		        
		        var pwd2 = "{$uinfo.pwd2}";
		        if (pwd2 == '') {
		            $(document).dialog({infoText: '{:lang('Nofundsprotectionpasswordisset')}'});
		            setTimeout(function () {
		            // 	window.location.href = '/index/ctrl/edit_deposit_pwd.html';
		            }, 2000);
		        }
		        
		        /*检查表单*/
		        function check() {
		            if ($("input[name=username]").val() == '' || $("input[name=card_bak]").val() == '' || $("select[name=wallet_type]").val() == '') {
		                $(document).dialog({infoText: '{:lang("必填项不能为空")}'});
		                return false;
		            }
		            return true;
		        }
		
		        /*点击保存*/
		        $(".save-btn").on('click', function () {
		            if (check()) {
		                var loading = null;
		                $.ajax({
		                    url: '/index/my/bind_wallet',
		                    data: $("#login-form").serialize(),
		                    type: 'POST',
		                    beforeSend: function () {
		                        loading = $(document).dialog({
		                            type: 'notice',
		                            infoIcon: '/static_new/img/loading.gif',
		                            infoText: '{:lang('z_Loading')}',
		                            autoClose: 0
		                        });
		                    },
		                    success: function (data) {
		                        if (data.code == 0) {
		                            $(document).dialog({infoText: '{:lang('Savedsuccessfully')}'});
		                            
		                            setTimeout(function(){ 
		                                    location.href="/index/my/index";
		                               },1000);
		                               
		                        } else {
		                            loading.close();
		                            $(document).dialog({infoText: data.info});
		                        }
		                    }
		                });
		            }
		            return false;
		        })
		    })
		
		</script>
	</body>
</html> 