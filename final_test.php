<?php
/**
 * 最终路由测试页面
 * 验证修复后的路由是否正常工作
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>最终路由测试</h1>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
.success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
.error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
.warning { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
.info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
.test-link { 
    display: inline-block; 
    margin: 5px; 
    padding: 10px 15px; 
    background: #007bff; 
    color: white; 
    text-decoration: none; 
    border-radius: 5px;
}
.test-link:hover { background: #0056b3; }
pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
</style>";

// 检查修复状态
echo "<div class='test-section info'>";
echo "<h2>修复状态检查</h2>";

$fixes_applied = [
    '路由优先级调整' => true,
    'Error控制器修复' => true,
    'JavaScript重定向移除' => true,
    '用户认证逻辑改进' => true
];

foreach ($fixes_applied as $fix => $status) {
    $icon = $status ? "✅" : "❌";
    echo "<p>{$icon} {$fix}</p>";
}
echo "</div>";

// 路由配置检查
echo "<div class='test-section info'>";
echo "<h2>路由配置验证</h2>";

if (file_exists('route/route.php')) {
    $route_content = file_get_contents('route/route.php');
    
    // 检查高优先级路由
    $priority_routes = [
        "Route::rule('index/ctrl/recharge_admin'" => '充值记录路由',
        "Route::rule('index/ctrl/junior'" => '团队报告路由',
        "Route::rule('index/ctrl/deposit_admin'" => '提现记录路由'
    ];
    
    foreach ($priority_routes as $route => $desc) {
        $found = strpos($route_content, $route) !== false;
        $icon = $found ? "✅" : "❌";
        echo "<p>{$icon} {$desc}: " . ($found ? "已配置" : "未配置") . "</p>";
    }
    
    // 检查问题配置是否已注释
    $problematic_configs = [
        'Route::miss(' => '404重定向配置',
        "Route::rule('/', 'index/Ctrl/recharge')" => '根路径重定向配置'
    ];
    
    echo "<h3>问题配置检查:</h3>";
    foreach ($problematic_configs as $config => $desc) {
        $found = strpos($route_content, $config) !== false;
        $commented = strpos($route_content, '//' . $config) !== false || 
                    strpos($route_content, '// ' . $config) !== false;
        
        if ($found && !$commented) {
            echo "<p>❌ {$desc}: 存在且未注释 (可能导致问题)</p>";
        } elseif ($found && $commented) {
            echo "<p>✅ {$desc}: 已注释 (已修复)</p>";
        } else {
            echo "<p>✅ {$desc}: 不存在</p>";
        }
    }
} else {
    echo "<p class='error'>❌ 路由配置文件不存在</p>";
}
echo "</div>";

// 控制器方法检查
echo "<div class='test-section info'>";
echo "<h2>控制器方法验证</h2>";

if (file_exists('application/index/controller/Ctrl.php')) {
    $ctrl_content = file_get_contents('application/index/controller/Ctrl.php');
    
    // 检查JavaScript重定向是否已移除
    $js_redirect_patterns = [
        'echo "<script>window.location.href=' => 'JavaScript重定向',
        'window.location.href' => 'JavaScript跳转代码'
    ];
    
    foreach ($js_redirect_patterns as $pattern => $desc) {
        $found = strpos($ctrl_content, $pattern) !== false;
        $icon = $found ? "❌" : "✅";
        $status = $found ? "仍存在 (需要修复)" : "已移除 (已修复)";
        echo "<p>{$icon} {$desc}: {$status}</p>";
    }
    
    // 检查方法是否存在
    $methods = [
        'public function recharge_admin(' => '充值记录方法',
        'public function junior(' => '团队报告方法',
        'public function deposit_admin(' => '提现记录方法'
    ];
    
    foreach ($methods as $method => $desc) {
        $found = strpos($ctrl_content, $method) !== false;
        $icon = $found ? "✅" : "❌";
        echo "<p>{$icon} {$desc}: " . ($found ? "存在" : "不存在") . "</p>";
    }
} else {
    echo "<p class='error'>❌ 控制器文件不存在</p>";
}
echo "</div>";

// 实际测试链接
echo "<div class='test-section warning'>";
echo "<h2>实际测试</h2>";
echo "<p><strong>请确保已登录系统，然后点击以下链接进行测试：</strong></p>";

$test_urls = [
    '/index/ctrl/recharge_admin' => '充值记录页面',
    '/index/ctrl/junior' => '团队报告页面',
    '/index/ctrl/deposit_admin' => '提现记录页面'
];

foreach ($test_urls as $url => $desc) {
    echo "<a href='{$url}' class='test-link' target='_blank'>{$desc}</a>";
}

echo "<p style='margin-top: 15px;'><strong>测试说明：</strong></p>";
echo "<ul>";
echo "<li>如果页面正常显示对应内容，说明修复成功</li>";
echo "<li>如果仍然跳转到首页，请检查浏览器控制台错误</li>";
echo "<li>如果出现404错误，请检查路由配置</li>";
echo "<li>如果出现权限错误，请确保已正确登录</li>";
echo "</ul>";
echo "</div>";

// 故障排除指南
echo "<div class='test-section info'>";
echo "<h2>故障排除指南</h2>";
echo "<ol>";
echo "<li><strong>清除缓存：</strong> 清除浏览器缓存和ThinkPHP路由缓存</li>";
echo "<li><strong>检查日志：</strong> 查看服务器错误日志和应用日志</li>";
echo "<li><strong>验证登录：</strong> 确保用户已正确登录且session有效</li>";
echo "<li><strong>检查权限：</strong> 确保用户有访问这些页面的权限</li>";
echo "<li><strong>重启服务：</strong> 重启Web服务器以确保配置生效</li>";
echo "</ol>";
echo "</div>";

echo "<div class='test-section success'>";
echo "<h2>修复总结</h2>";
echo "<p>本次修复主要解决了以下问题：</p>";
echo "<ul>";
echo "<li>移除了导致路由冲突的Route::miss配置</li>";
echo "<li>调整了路由优先级，确保特定路由优先匹配</li>";
echo "<li>修复了Error控制器的无条件重定向问题</li>";
echo "<li>移除了控制器中的JavaScript重定向代码</li>";
echo "<li>改进了用户认证逻辑，确保session和cookie同步</li>";
echo "</ul>";
echo "<p><strong>如果问题仍然存在，请检查服务器配置和ThinkPHP框架设置。</strong></p>";
echo "</div>";
?>
