<?php

namespace app\index\controller;

use think\Controller;
use think\Request;
use think\Db;
use think\Lang;

use payment\Payment;

class Ctrl extends Base
{
      //钱包页面
    public function wallet()
    {
        $balance = db('xy_users')->where('id',cookie('user_id'))->value('balance');
        $this->assign('balance',$balance);
        $balanceT = db('xy_convey')->where('uid',cookie('user_id'))->where('status',1)->sum('commission');
        $this->assign('balance_shouru',$balanceT);

        //收益
        $startDay = strtotime( date('Y-m-d 00:00:00', time()) );
        $shouyi = db('xy_convey')->where('uid',cookie('user_id'))->where('addtime','>',$startDay)->where('status',1)->select();

        //充值
        $chongzhi = db('xy_recharge')->where('uid',cookie('user_id'))->where('addtime','>',$startDay)->where('status',2)->select();

        //提现
        $tixian = db('xy_deposit')->where('uid',cookie('user_id'))->where('addtime','>',$startDay)->where('status',1)->select();

        $this->assign('shouyi',$shouyi);
        $this->assign('chongzhi',$chongzhi);
        $this->assign('tixian',$tixian);
        return $this->fetch();
    }

    // public function dd()
    // {
    //     $count = DB::table('xy_convey')->where('status','in',[1,3])->where('uid',cookie('user_id'))->count('*');
    //     $zp_count = [120,300,480];
    //     if(in_array($count,$zp_count)){
    //         Db::name('xy_users')->where('id', cookie('user_id'))->setInc('zp_num',1);
    //     }
    // }

    public function dial()
    {
        if(config('zp') == 2){
            result(lang('功能关闭'),'/index/my/index.html');
        }


        $awrad = [18.8,38.8,108.8,188,288,588];
        $pros   = [0   ,0   ,0    ,0  ,0  ,0  ];
        // $awrad = array_map(function($v){
        //     return $v.lang('元');
        // },$awrads);
        //
        $num = DB::table('xy_award')->where(['uid' => cookie('user_id')])->count('*');
        $data = [
            1,
            2,
            3,
            4,
            ];
        if(@isset($pros[$data[$num]])){
            $pros[$data[$num]] = 100;
        }else{
            $pros   = [25   ,15   ,15    ,15  ,15  ,15  ];
        }



        $pro = array_map(function($v){
            return $v.'%';
        },$pros);

        $zp_num = DB::table('xy_users')->where('id',cookie('user_id'))->value('zp_num');

        $this->assign('zp_num', $zp_num);
        $this->assign('pro', $pro);
        $this->assign('awrad',$awrad);

        return $this->fetch();
    }


    public function awrad()
    {

        $uid = cookie('user_id');
        $zp_num = DB::table('xy_users')->where('id',$uid)->value('zp_num');
        if($zp_num <= 0){
            echo json_encode(['msg' =>lang('抽奖次数不足') , 'code'=> 500]);
            exit;
        }

        $awrads = [18.8,38.8,108.8,188,288,588];
        $i = input('i');

        $num = $awrads[$i];
        // 抽奖记录
        DB::table('xy_award')->insert([
            'uid'=>$uid,
            'time'=>time(),
            'award'=>$num,
            ]);
        // 用户余额记录
        DB::table('xy_balance_log')->insert([
            'uid'=>$uid,
            'oid'=> getSn('ZP'),
            'num'=>$num,
            'type'=>87,
            'status'=>1,
            'addtime'=>time(),
            ]);
        DB::table('xy_users')->where(['id' => $uid])->setInc('balance',$num);
        DB::table('xy_users')->where(['id' => $uid])->setDec('zp_num',1);
        echo json_encode(['msg' =>lang('恭喜获得').$num , 'code'=> 200]);
        exit;
    }

// public function edit_deposit_pwd(){
//     $this->pwd2 =$r= db('xy_users')->where('id',cookie('user_id'))->value('pwd2');
//     dump($r);
//     return $this->fetch();
// }
    public function recharge_before()
    {
        $pay = db('xy_pay')->where('status',1)->select();

        $this->assign('pay',$pay);
        return $this->fetch();
    }


    public function vip()
    {
        // 获取用户ID，优先使用session，其次使用cookie
        $uid = session('user_id');
        if (!$uid) {
            $uid = cookie('user_id');
        }
        if (!$uid) {
            return $this->redirect('User/login');
        }

        // 查询支付方式 - 如果查询失败使用空数组
        $pay = db('xy_pay')->where('status', 1)->select();
        if ($pay === false) {
            $pay = [];
        }
        
        // 查询等级列表 - 如果查询失败使用空数组
        $member_level = db('xy_level')->order('level asc')->select();
        if ($member_level === false) {
            $member_level = [];
        }
        
        // 查询用户信息
        $info = db('xy_users')->where('id', $uid)->find();
        if (!$info) {
            return $this->redirect('User/login');
        }

        // 获取用户等级信息
        $user_level = $info['level'] ?? 0;

        // 默认值
        $level_name = 'VIP0';
        $order_num = 1;

        // 如果用户有等级，查找对应的等级信息
        if (!empty($user_level)) {
            // 先尝试通过id字段查找
            $level_info = db('xy_level')->where('id', $user_level)->find();
            
            // 如果没找到，尝试通过level字段查找
            if (!$level_info) {
                $level_info = db('xy_level')->where('level', $user_level)->find();
            }

            if ($level_info) {
                $level_name = $level_info['name'] ?? 'VIP' . $user_level;
                $order_num = $level_info['order_num'] ?? 1;
            } else {
                // 如果都没找到，根据用户level生成默认名称
                $level_name = 'VIP' . $user_level;
            }
        }

        // 使用assign方法分配变量到模板
        $this->assign([
            'member_level' => $member_level,
            'info' => $info,
            'member' => $info,
            'level_name' => $level_name,
            'order_num' => $order_num,
            'list' => $pay
        ]);

        // 直接返回模板，不使用try-catch
        return $this->fetch();
    }

    public function qubao()
    {
        // 获取用户ID，优先使用session，其次使用cookie
        $uid = session('user_id');
        if (!$uid) {
            $uid = cookie('user_id');
        }
        if (!$uid) {
            return $this->redirect('User/login');
        }

        $uinfo = db('xy_users')->field('username,tel,level,id,headpic,balance,freeze_balance,lixibao_balance,lixibao_dj_balance')->find($uid);
        $data = [
          "balance" => ($uinfo['balance'] + $uinfo['lixibao_balance']),
          "freeze_balance" => "0.00",
          "lixibao_balance" => ($uinfo['lixibao_balance'] - $uinfo['lixibao_balance']),
          "lixibao_dj_balance" => "0.0000"
        ];
        db('xy_lixibao')->where('uid', $uid)->delete();
        $res = db('xy_users')->where('id', $uid)->update($data);
        if($res){
           return 1;
        }else {
           return 2;
        }
    }

    /**
     * @地址      recharge_dovip
     * @说明      利息宝
     * @参数       @参数 @参数
     * @返回      \think\response\Json
     */
    public function lixibao()
    {
        $this->assign('title',lang('利息宝'));
        $uinfo = db('xy_users')->field('username,tel,level,id,headpic,balance,freeze_balance,lixibao_balance,lixibao_dj_balance')->find(cookie('user_id'));
        // dump($uinfo);exit;
        $this->assign('ubalance',$uinfo['balance']);
        $this->assign('balance',$uinfo['lixibao_balance']);
        $this->assign('balance_total',$uinfo['lixibao_balance'] + $uinfo['lixibao_dj_balance']);

        // 获取总收益（使用status=1确保只统计已结算的收益）
        $balanceT = db('xy_balance_log')->where('uid',cookie('user_id'))->where('status',1)->where('type',23)->sum('num');
        if(!$balanceT) $balanceT = 0;

        // 获取昨日收益
        $yes1 = strtotime(date("Y-m-d 00:00:00",strtotime("-1 day")));
        $yes2 = strtotime(date("Y-m-d 23:59:59",strtotime("-1 day")));
        $yes_shouyi = db('xy_balance_log')->where('uid',cookie('user_id'))->where('status',1)->where('type',23)->where('addtime','between',[$yes1,$yes2])->sum('num');
        if(!$yes_shouyi) $yes_shouyi = 0;

        $this->assign('balance_shouru', number_format($balanceT, 2));
        $this->assign('yes_shouyi', number_format($yes_shouyi, 2));

        //收益
        $startDay = strtotime(date('Y-m-d 00:00:00', time()));
        $shouyi = db('xy_lixibao')->where('uid',cookie('user_id'))->where('is_qu',0)->select();

        foreach ($shouyi as &$item) {
            $type = '';
            if ($item['type'] == 1) {
                $type = '<font color="green">'.lang('余额宝转入').'</font>';
            }elseif ($item['type'] == 2) {
                $n = $item['status'] ? lang('已到账') : lang('未到账');
                $type = '<font color="red" >'.lang('余额宝转出').'('.$n.')</font>';
            }elseif ($item['type'] == 3) {
                $type = '<font color="orange" >'.lang('每日收益').'</font>';
            }else{

            }

            $lixbao = Db::name('xy_lixibao_list')->find($item['sid']);

            $name = $lixbao['name'].'('.$lixbao['day'].lang('天)').$lixbao['bili']*100 .'% ';

            $item['num'] = number_format($item['num'],2);
            $item['name'] = $type.'　　'.$name;
            $item['shouxu'] = $lixbao['shouxu']*100 .'%';
            $item['addtime'] = date('Y/m/d H:i', $item['addtime']);

            if ($item['is_sy'] == 1) {
                $notice = lang('正常收益,实际收益').$item['real_num'];
            }else if ($item['is_sy'] == -1) {
                $notice = lang('未到期提前提取,未收益,手续费为:').$item['shouxu'];
            }else{
                $notice = lang('理财中...');
            }
            $item['notice'] =$notice;
        }

        // 直接查询所有与余额宝相关的余额变动记录（最近30天，限制为3条）
        $month_ago = strtotime('-30 days');
        $income_records = db('xy_balance_log')
            ->where('uid', cookie('user_id'))
            ->where('status', 1)
            ->whereIn('type', [21, 22, 23]) // 包括转入(21)、转出(22)和收益(23)
            ->where('addtime', '>', $month_ago)
            ->order('addtime desc')
            ->limit(3) // 减少显示数量，避免页面太长
            ->select();

        foreach ($income_records as &$record) {
            // 根据类型设置不同的标题
            if ($record['type'] == 21) {
                $record['title'] = lang('余额宝转入');
                $record['title_en'] = 'Deposit to Balance Savings';
                $record['color'] = 'green';
            } elseif ($record['type'] == 22) {
                $record['title'] = lang('余额宝转出');
                $record['title_en'] = 'Withdraw from Balance Savings';
                $record['color'] = 'red';
            } elseif ($record['type'] == 23) {
                $record['title'] = lang('每日收益');
                $record['title_en'] = 'Daily Interest';
                $record['color'] = 'blue';
            } else {
                $record['title'] = lang('余额变动');
                $record['title_en'] = 'Balance Change';
                $record['color'] = 'blue';
            }

            $record['addtime_fmt'] = date('Y-m-d H:i', $record['addtime']);
            $record['num'] = number_format($record['num'], 2);
        }

        // 获取最近的转入转出记录（限制为3条）
        $transfer_records = db('xy_lixibao')
            ->where('uid', cookie('user_id'))
            ->where('type', 'in', [1, 2])
            ->order('addtime desc')
            ->limit(3) // 减少显示数量，避免页面太长
            ->select();

        foreach ($transfer_records as &$record) {
            if ($record['type'] == 1) {
                $record['type_text'] = '<font color="green">'.lang('余额宝转入').'</font>';
                $record['type_text_en'] = '<font color="green">Transfer in</font>';
            } else {
                $n = $record['status'] ? lang('已到账') : lang('未到账');
                $n_en = $record['status'] ? 'Completed' : 'Pending';
                $record['type_text'] = '<font color="red">'.lang('余额宝转出').'('.$n.')</font>';
                $record['type_text_en'] = '<font color="red">Transfer out ('.$n_en.')</font>';
            }
            $record['addtime_fmt'] = date('Y-m-d H:i', $record['addtime']);
            $record['num'] = number_format($record['num'], 2);
        }

        $this->assign('income_records', $income_records);
        $this->assign('transfer_records', $transfer_records);

        // 获取利率配置
        // 从配置文件获取利率
        $rate = config('lxb_bili');

        // 如果利率为0或不存在，则尝试从数据库获取最新利率
        if (empty($rate) || $rate == '0') {
            // 尝试从数据库中获取最新的利率设置
            try {
                // 查询余额宝产品列表中的最新利率
                $latest_rate = Db::name('xy_lixibao_list')
                    ->where('status', 1)
                    ->order('addtime desc')
                    ->value('bili');

                if (!empty($latest_rate) && $latest_rate > 0) {
                    $rate = floatval($latest_rate);
                } else {
                    $rate = 0.05; // 默认利率5%
                }
            } catch (\Exception $e) {
                $rate = 0.05; // 如果查询失败，使用默认值
            }
        } else {
            $rate = floatval($rate);
        }

        $this->rililv = $rate*100 .'%' ;
        $this->shouyi=$shouyi;
        if(request()->isPost()) {
            return json(['code'=>0,'info'=>lang('操作'),'data'=>$shouyi]);
        }

        $lixibao = Db::name('xy_lixibao_list')->field('id,name,bili,day,min_num')->order('day asc')->select();
        $this->lixibao = $lixibao;
        return $this->fetch();
    }

    public function lixibao_ru()
    {

        $uid = cookie('user_id');
        $uinfo = Db::name('xy_users')->field('recharge_num,deal_time,balance,level')->find($uid);//获取用户今日已充值金额

        if(request()->isPost()){
            $count = db('xy_lixibao')->where('uid', cookie('user_id'))->find();
            $price = input('post.price/d',0);
            $id = input('post.cid/d',0);
            $yuji=0;
            if ($id) {
                $lixibao = Db::name('xy_lixibao_list')->find($id);
                if ($price < $lixibao['min_num']) {
                    return json(['code'=>1,'info'=>lang('该产品最低起投金额').$lixibao['min_num']]);
                }
                if ($price > $lixibao['max_num']) {
                    return json(['code'=>1,'info'=>lang('该产品最高可投金额').$lixibao['max_num']]);
                }
                $yuji = $price * $lixibao['bili'] * $lixibao['day'];
            }else{
                return json(['code'=>1,'info'=>lang('数据异常')]);
            }


            if ( $price <= 0 ) {
                return json(['code'=>1,'info'=>'you are sb']); //直接充值漏洞
            }
            if ($uinfo['balance'] < $price ) {
                return json(['code'=>1,'info'=>lang('可用余额不足，请充值')]);
            }
            Db::name('xy_users')->where('id',$uid)->setInc('lixibao_balance',$price);  //利息宝月 +
            Db::name('xy_users')->where('id',$uid)->setDec('balance',$price);  //余额 -

            $endtime = time() + $lixibao['day'] * 24 * 60 * 60;
            // if(!$count){
                $res = Db::name('xy_lixibao')->insert([
                    'uid'         => $uid,
                    'num'         => $price,
                    'addtime'     => time(),
                    'endtime'     => $endtime,
                    'sid'         => $id,
                    'yuji_num'         => $yuji,
                    'type'        => 1,
                    'status'      => 0,
                    'day'         => $lixibao['day'],
                    'shouxu'         => $lixibao['shouxu'],
                     'bili'         => $lixibao['bili'],
                ]);
            // }else{
            //     $res = Db::name('xy_lixibao')->where('uid', cookie('user_id'))->update([
            //         'uid'         => $uid,
            //         'num'         => ($count['num'] + $price),
            //         'addtime'     => time(),
            //         'endtime'     => $endtime,
            //         'sid'         => $id,
            //         'yuji_num'         => ($count['yuji_num'] + $yuji),
            //         'type'        => 1,
            //         'status'      => 0,
            //         'day'         => $lixibao['day'],
            //     ]);
            // }
            $oid = Db::name('xy_lixibao')->getLastInsID();
            $res1 = Db::name('xy_balance_log')->insert([
                //记录返佣信息
                'uid'       => $uid,
                'oid'       => $oid,
                'num'       => $price,
                'type'      => 21,
                'addtime'   => time()
            ]);
            if($res) {
                return json(['code'=>0,'info'=>lang('操作成功')]);
            }else{
                return json(['code'=>1,'info'=>lang('操作失败!请检查账号余额')]);
            }
        }

        // 获取利率配置
        // 从配置文件获取利率
        $rate = config('lxb_bili');

        // 如果利率为0或不存在，则尝试从数据库获取最新利率
        if (empty($rate) || $rate == '0') {
            // 尝试从数据库中获取最新的利率设置
            try {
                // 查询余额宝产品列表中的最新利率
                $latest_rate = Db::name('xy_lixibao_list')
                    ->where('status', 1)
                    ->order('addtime desc')
                    ->value('bili');

                if (!empty($latest_rate) && $latest_rate > 0) {
                    $rate = floatval($latest_rate);
                } else {
                    $rate = 0.05; // 默认利率5%
                }
            } catch (\Exception $e) {
                $rate = 0.05; // 如果查询失败，使用默认值
            }
        } else {
            $rate = floatval($rate);
        }

        $this->rililv = $rate*100 .'%' ;
        $this->yue = $uinfo['balance'];
        $isajax = input('get.isajax/d',0);

        if ($isajax) {
            $lixibao = Db::name('xy_lixibao_list')->field('id,name,bili,day,min_num')->select();
            $data2=[];
            $str = $lixibao[0]['name'].'('.$lixibao[0]['day'].lang('天)').$lixibao[0]['bili']*100 .'% ('.$lixibao[0]['min_num'].lang('起投)');
            foreach ($lixibao as $item) {
                $data2[] = array(
                    'id'=>$item['id'],
                    'value'=>$item['name'].'('.$item['day'].lang('天)').$item['bili']*100 .'% ('.$item['min_num'].lang('起投)'),
                );
            }
            return json(['code'=>0,'info'=>lang('操作'),'data'=>$data2,'data0'=>$str]);
        }

        $this->libi =1;

        $this->assign('title',lang('利息宝余额转入'));
        return $this->fetch();
    }


    public function deposityj()
    {
        $num = input('post.price/f',0);
        $id = input('post.cid/d',0);
        if ($id) {
            $lixibao = Db::name('xy_lixibao_list')->find($id);

            $res = $num * $lixibao['day'] * $lixibao['bili'];
            return json(['code'=>0,'info'=>lang('操作'),'data'=>$res]);
        }
    }

    /**
     * 余额宝收益记录
     */
    public function lixibao_income()
    {
        $uid = cookie('user_id');
        $uinfo = Db::name('xy_users')->field('recharge_num,deal_time,balance,level,lixibao_balance')->find($uid);

        // 获取利率配置
        // 从配置文件获取利率
        $rate = config('lxb_bili');

        // 如果利率为0或不存在，则尝试从数据库获取最新利率
        if (empty($rate) || $rate == '0') {
            // 尝试从数据库中获取最新的利率设置
            try {
                // 查询余额宝产品列表中的最新利率
                $latest_rate = Db::name('xy_lixibao_list')
                    ->where('status', 1)
                    ->order('addtime desc')
                    ->value('bili');

                if (!empty($latest_rate) && $latest_rate > 0) {
                    $rate = floatval($latest_rate);
                } else {
                    $rate = 0.05; // 默认利率5%
                }
            } catch (\Exception $e) {
                $rate = 0.05; // 如果查询失败，使用默认值
            }
        } else {
            $rate = floatval($rate);
        }

        $this->rililv = $rate*100 .'%';
        $this->yue = $uinfo['lixibao_balance'];

        // 筛选条件
        $start_time = input('get.start_time/s','');
        $end_time = input('get.end_time/s','');
        $type = input('get.type/d', 0);

        // 构建查询基础条件
        $whereCondition = [
            ['uid', '=', $uid],
            ['status', '=', 1]
        ];

        // 增加日期筛选条件
        if (!empty($start_time) && !empty($end_time)) {
            $start_timestamp = strtotime("{$start_time} 0:0:0");
            $end_timestamp = strtotime("{$end_time} 23:59:59");
            $whereCondition[] = ['addtime', 'between', [$start_timestamp, $end_timestamp]];
        } else {
            // 默认只查询最近30天
            $month_ago = strtotime('-30 days');
            $whereCondition[] = ['addtime', '>', $month_ago];
        }

        // 增加类型筛选条件
        if ($type > 0) {
            $whereCondition[] = ['type', '=', 20 + $type]; // 21=转入, 22=转出, 23=收益
        } else {
            $whereCondition[] = ['type', 'in', [21, 22, 23]]; // 包括转入(21)、转出(22)和收益(23)
        }

        try {
            // 使用高效查询，按日期降序排序，并限制每次请求的数据量
            $list = Db::name('xy_balance_log')
                ->where($whereCondition)
                ->field('id, uid, oid, num, type, addtime')
                ->order('addtime desc, id desc')
                ->paginate(10);  // 减少每页显示数量，避免页面过长

            // 处理查询结果
            $list->each(function($item) {
                try {
                    // 根据类型设置不同的标题
                    if ($item['type'] == 21) {
                        // 获取转入的理财产品类型信息
                        $product_info = null;

                        try {
                            if (!empty($item['oid'])) {
                                // 简化的查询方式
                                $lixibao_record = Db::name('xy_lixibao')->where('id', $item['oid'])->find();

                                if (!empty($lixibao_record) && !empty($lixibao_record['sid'])) {
                                    $product_info = Db::name('xy_lixibao_list')->field('id, name')->find($lixibao_record['sid']);
                                }
                            }
                        } catch (\Exception $e) {
                            // 查询异常时不阻止继续执行
                            \think\facade\Log::error('查询产品信息异常: ' . $e->getMessage());
                        }

                        $item['title'] = lang('余额宝转入');
                        $item['title_en'] = 'Deposit to Balance Savings';

                        // 如果找到产品信息，添加产品名称
                        if (!empty($product_info) && isset($product_info['name'])) {
                            $item['product_type'] = $product_info['name'];
                            $item['title'] = lang('余额宝转入') . ' - ' . $product_info['name'];
                            $item['title_en'] = 'Deposit to ' . $product_info['name'];
                        } else {
                            // 使用Type F作为默认产品类型
                            $item['product_type'] = 'Type F';
                        }

                        $item['color'] = 'green';
                        $item['tag_class'] = 'tag-deposit';
                        $item['tag_text'] = lang('余额宝转入');
                        $item['tag_text_en'] = 'Deposit';
                    } elseif ($item['type'] == 22) {
                        // 转出类型
                        $item['title'] = lang('余额宝转出');
                        $item['title_en'] = 'Withdraw from Balance Savings';

                        // 默认产品类型
                        $item['product_type'] = 'Type F';

                        $item['color'] = 'red';
                        $item['tag_class'] = 'tag-withdraw';
                        $item['tag_text'] = lang('余额宝转出');
                        $item['tag_text_en'] = 'Withdrawal';
                    } elseif ($item['type'] == 23) {
                        // 收益类型
                        $item['title'] = lang('每日收益');
                        $item['title_en'] = 'Daily Interest';

                        // 默认产品类型和利率
                        $item['product_type'] = 'Type F';
                        $item['interest_rate'] = '5.0%';

                        $item['color'] = 'orange';
                        $item['tag_class'] = 'tag-interest';
                        $item['tag_text'] = lang('每日收益');
                        $item['tag_text_en'] = 'Interest';
                    } else {
                        $item['title'] = lang('余额变动');
                        $item['title_en'] = 'Balance Change';
                        $item['product_type'] = '';
                        $item['color'] = 'blue';
                        $item['tag_class'] = 'tag-other';
                        $item['tag_text'] = lang('余额变动');
                        $item['tag_text_en'] = 'Balance Change';
                    }

                    // 确保金额显示正确，避免显示-0.00
                    $item['num'] = abs($item['num']) < 0.01 ? '0.00' : number_format($item['num'], 2);
                    $item['addtime_fmt'] = date('Y-m-d H:i:s', $item['addtime']);
                } catch (\Exception $e) {
                    // 处理单条记录的异常，确保整体循环不会中断
                    \think\facade\Log::error('处理记录异常: ' . $e->getMessage());

                    // 设置默认值确保页面不会崩溃
                    $item['title'] = lang('余额变动');
                    $item['title_en'] = 'Balance Change';
                    $item['product_type'] = '';
                    $item['color'] = 'blue';
                    $item['tag_class'] = 'tag-other';
                    $item['tag_text'] = lang('余额变动');
                    $item['tag_text_en'] = 'Balance Change';
                    $item['num'] = number_format(abs($item['num']), 2);
                    $item['addtime_fmt'] = date('Y-m-d H:i:s', $item['addtime']);
                }

                return $item;
            });

        } catch (\Exception $e) {
            // 如果查询出错，记录错误并返回一个空集合
            \think\facade\Log::error('查询余额宝记录异常: ' . $e->getMessage());
            $list = new \think\paginator\driver\Bootstrap([], 0, 1, 0);
        }

        $this->assign('list', $list);
        $this->assign('title', lang('余额宝收益记录'));
        $this->assign('pagehtml', $list->render());

        return $this->fetch();
    }

    public function lixibao_chu()
    {
        $uid = cookie('user_id');
        $uinfo = Db::name('xy_users')->field('recharge_num,deal_time,balance,level,lixibao_balance')->find($uid);//获取用户今日已充值金额

        if(request()->isPost()){
            // 检查是否有未取出的余额宝记录
            $lixibao = Db::name('xy_lixibao')->where('uid',$uid)->where('is_qu',0)->select();
            if (empty($lixibao)) {
                return json(['code'=>1,'info'=>lang('没有订单')]);
            }

            // 开始事务
            Db::startTrans();
            try {
                // 计算总转出金额和总收益
                $total_amount = 0;
                $total_price = 0;

                foreach($lixibao as $v){
                    // 先将余额宝记录标记为已取出
                    Db::name('xy_lixibao')->where('id',$v['id'])->update([
                        'is_qu' => 1,
                    ]);

                    // 累加转出金额
                    $total_amount += $v['num'];

                    // 计算应扣除的手续费和实际到账金额
                    $shouxu = $v['shouxu'];
                    $item_price = 0;
                    if ($shouxu) {
                        $item_price = $v['num'] - $v['num']*$shouxu;
                        $item_price += $v['sum_shouyi']; // 加上这些天的收益
                    }

                    // 累加实际到账金额
                    $total_price += $item_price;
                }

                // 扣除用户余额宝余额
                Db::name('xy_users')->where('id',$uid)->setDec('lixibao_balance', $total_amount);

                // 增加用户余额
                Db::name('xy_users')->where('id',$uid)->setInc('balance', $total_price);

                // 只创建一条余额变动记录，避免出现大量重复记录
                // 确保金额大于0才记录，避免出现0.00的记录
                if ($total_price > 0) {
                    Db::name('xy_balance_log')->insert([
                        'uid'       => $uid,
                        'oid'       => getSn('LXBFY'),
                        'num'       => $total_price,
                        'type'      => 22,
                        'status'    => 1,
                        'addtime'   => time()
                    ]);
                }

                // 提交事务
                Db::commit();
                return json(['code'=>0,'info'=>lang('操作成功')]);

            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                return json(['code'=>1,'info'=>lang('操作失败!') . $e->getMessage()]);
            }
        }

        $this->assign('title',lang('利息宝余额转出'));
        // 获取利率配置
        // 从配置文件获取利率
        $rate = config('lxb_bili');

        // 如果利率为0或不存在，则尝试从数据库获取最新利率
        if (empty($rate) || $rate == '0') {
            // 尝试从数据库中获取最新的利率设置
            try {
                // 查询余额宝产品列表中的最新利率
                $latest_rate = Db::name('xy_lixibao_list')
                    ->where('status', 1)
                    ->order('addtime desc')
                    ->value('bili');

                if (!empty($latest_rate) && $latest_rate > 0) {
                    $rate = floatval($latest_rate);
                } else {
                    $rate = 0.05; // 默认利率5%
                }
            } catch (\Exception $e) {
                $rate = 0.05; // 如果查询失败，使用默认值
            }
        } else {
            $rate = floatval($rate);
        }

        $this->rililv = $rate*100 .'%' ;
        $this->yue = $uinfo['lixibao_balance'] ;

        $query = $this->_query('xy_lixibao')->where('uid',cookie('user_id'))->order('addtime desc');

        $start_time = input('get.start_time/s','');
        $end_time =input('get.end_time/s','');
        $is_qu = input('get.is_qu/d',2);
        $where = [];
        if( !empty($start_time) && !empty($end_time)){
        	$start_time = strtotime("{$start_time} 0:0:0" );
        	$end_time = strtotime("{$end_time} 23:59:59");

        	 $query->where("addtime >={$start_time} and addtime <= {$end_time}");
        }

        if( $is_qu !=2){
        	$query->where(['is_qu'=>$is_qu]);
        }

		$query->page();
    }



    //升级vip
    public function recharge_dovip()
    {

        if(request()->isPost()){
            $level = input('post.level/d',1);
            $type = input('post.type/s','');

            $uid = cookie('user_id');
            $uinfo = db('xy_users')->field('pwd,salt,tel,username,balance')->find($uid);
            if(!$level ) return json(['code'=>1,'info'=>lang('参数错误')]);

            //
            $pay = db('xy_pay')->where('id',$type)->find();
            $level_info = db('xy_level')->where('level',$level)->find();
            $num = $level_info['num'];
            // db('xy_level')->where('level',$level)->value('num');;

            if ($num > $uinfo['balance']) {
                return json(['code'=>1,'info'=>lang('可用余额不足，请充值')]);
            }



            $id = getSn('SY');
            $res = db('xy_recharge')
                ->insert([
                    'id'        => $id,
                    'uid'       => $uid,
                    'tel'       => $uinfo['tel'],
                    'real_name' => $uinfo['username'],
                    'pic'       => '',
                    'num'       => $num,
                    'addtime'   => time(),
                    'pay_name'  => $type,
                    'is_vip'    => 1,
                    'level'     =>$level
                ]);
            if($res){
                if ($type == 999) {
                    $level_validity = $level_info['validity'] > 0 ? date('Y-m-d H:i:s', time() + $level_info['validity'] * 24 * 3600) : 99;
                    $res1 = Db::name('xy_users')->where('id',$uid)->update(['level'=>$level, 'level_validity' => $level_validity]);
                    $res1 = Db::name('xy_users')->where('id',$uid)->setDec('balance',$num);
                    $res = Db::name('xy_recharge')->where('id',$id)->update(['endtime'=>time(),'status'=>2]);


                    $res2 = Db::name('xy_balance_log')
                        ->insert([
                            'uid'=>$uid,
                            'oid'=>$id,
                            'num'=>$num,
                            'type'=>30,
                            'status'=>1,
                            'addtime'=>time(),
                        ]);
                    return json(['code'=>0,'info'=>lang('升级成功')]);
                }



                $pay['id'] = $id;
                $pay['num'] =$num;
                if ($pay['name2'] == 'bipay' ) {
                    $pay['redirect'] = url('/index/Api/bipay').'?oid='.$id;
                }
                if ($pay['name2'] == 'paysapi' ) {
                    $pay['redirect'] = url('/index/Api/pay').'?oid='.$id;
                }

                if ($pay['name2'] == 'card' ) {
                    $pay['master_cardnum']= config('master_cardnum');
                    $pay['master_name']= config('master_name');
                    $pay['master_bank']= config('master_bank');
                }

                return json(['code'=>0,'info'=>$pay]);
            }

            else
                return json(['code'=>1,'info'=>lang('提交失败，请稍后再试')]);
        }
        return json(['code'=>0,'info'=>lang('请求成功'),'data'=>[]]);
    }


    public function charge(){
        $uid = cookie('user_id');
        if(!$uid){
            return $this->redirect('User/login');
        }

        if($_POST){


            $pic = input('post.pic/s','');
            $money = input('post.money','');

            $uid = cookie('user_id');

            if (is_image_base64($pic))
                $pic = '/' . $this->upload_base64('xy',$pic);  //调用图片上传的方法
            else
                return json(['code'=>1,'info'=>lang('图片格式错误')]);


           if($money <= 0.000001){
               return json(['code'=>1,'info'=>lang('请输入数量')]);

           }
           $userData = Db::table('xy_users')->find($uid);

           $input['uid'] = $uid;
           $input['id'] = $orderId = getSn('SY');
           $input['real_name'] = $userData['username'];
           $input['tel'] = $userData['tel'];
           $input['num'] = $money;
           $input['pic'] = $pic;
           $input['addtime'] = time();

           $res = Db::table('xy_recharge')->insert($input);

            if($res){
                return json(['code'=>2,'info'=>lang('充值成功等待审核')]);
            }
            return json(['code'=>1,'info'=>lang('参数错误')]);





        }

       // $tel = Db::name('xy_users')->where('id',$uid)->value('tel');//获取用户今日已充值金额


        return $this->fetch();
    }


    public function paytm(){
        $num = input('num/f',0);

        $pay = db('xy_pay')->where('name2','paytm')->find();
         if ($num < $pay['min']) return json(['code'=>1,'info'=>lang('充值不能小于').$pay['min']]);
        if ($num > $pay['max']) return json(['code'=>1,'info'=>lang('充值不能大于').$pay['max']]);

        $uid = cookie('user_id');

        $xy_user = db('xy_users')->find($uid);
        $orderId = getSn('SY');
        $slhttp = ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') || (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] == 'https')) ? 'https://' : 'http://';
        $start_url="https://interface.sskking.com/pay/web";
        $merchant_key ="08ac3953933b4fc8a0ce425c424b8934";

        $mch_id = '700222101';
    	$page_url = $slhttp. $_SERVER['SERVER_NAME'];
    	$notify_url = $page_url.'/index/api/paytm_hui';
    	$mch_order_no = $orderId;
    	$pay_type ='772';
    	$trade_amount = $num;
    	$order_date = date('Y-m-d H:i:s');
    	$goods_name = 'recharge';
    	$sign_type = 'MD5';
    	$signStr = "";
    	if($goods_name != ""){
    		$signStr = $signStr."goods_name=".$goods_name."&";
    	}
    	$signStr = $signStr."mch_id=".$mch_id."&";
    	$signStr = $signStr."mch_order_no=".$mch_order_no."&";
    // 	$signStr = $signStr."mch_return_msg=".$mch_return_msg."&";
    	$signStr = $signStr."notify_url=".$notify_url."&";
    	$signStr = $signStr."order_date=".$order_date."&";


    	$signStr = $signStr."page_url=".$page_url."&";
    	$signStr = $signStr."pay_type=".$pay_type."&";
    	$signStr = $signStr."trade_amount=".$trade_amount;
    	$signStr = $signStr."&version=1.0";
        include('SignApi.php');

        $signAPI = new \SignApi;
        $sign = $signAPI->sign($signStr,$merchant_key);
        $signStr .=  "&sign_type=".$sign_type;
        $signStr .= '&sign='.$sign;
        $result = $signAPI->http_post_res($start_url, $signStr);
        $repones = json_decode($result, true);
        if(!$result){
            $url = '/index/ctrl/recharge';
        }else{
            $res = db('xy_recharge')
                ->insert([
                    'id'        => $orderId,
                    'uid'       => $uid,
                    'tel'       => is_null($xy_user['tel']) ? '' : $xy_user['tel'],
                    'real_name' => $xy_user['username'],
                    'pic'       => 'api',
                    'num'       => $num,
                    'addtime'   => time(),
                    'pay_name'  => 'paytm'
                ]);
            $url = $repones['payInfo'];
        }
        return $this->redirect($url);
    }

    public function kbpay(){
        $num = sprintf('%.2f', input('num/f',0));


        //
        $pay = db('xy_pay')->where('name2','kbpay')->find();
         if ($num < $pay['min']) return json(['code'=>1,'info'=>lang('充值不能小于').$pay['min']]);
        if ($num > $pay['max']) return json(['code'=>1,'info'=>lang('充值不能大于').$pay['max']]);

        $uid = cookie('user_id');

        $xy_user = db('xy_users')->find($uid);
            $orderId = getSn('SY');

            $start_url = 'https://pay.3kbpay.com/india/recharge';
            $mch_id = '12135';
            $merchant_key ="c629e8609ce7ca20c91c2f2d52cd91bd";


        //     $mch_id = '12002';
        // 	$merchant_key = 'a5938ded6843fc86568deb452a5da697';


//         	merchant_id	是	string	商户号
// pay_type	是	int	⽀支付类型：
// 101	UPI
// 102	银⾏行行卡103	原⽣生
// order_id	是	string	商户订单号（唯⼀一）
// name	否	string	⽤用户姓名
// phone	否	string	⽤用户⼿手机号
// email	否	string	邮箱
// amount	是	float	订单⾦金金额
// redirect_url	否	string	付款成功后跳转地址
// remark	否	string	订单备注
// notify_url	否	string	异步通知地址
// sign	是	string	签名，签名规则看开头说明


        	$page_url = 'https://'. $_SERVER['SERVER_NAME'];
        	$notify_url = $page_url.'/index/api/kbpay_hui';

            $data['merchant_id'] = $mch_id;
            $data['pay_type'] = '101';
            $data['order_id'] = $orderId;
            $data['amount'] = $num;
            $data['notify_url'] = $notify_url;
            $data['redirect_url'] = $page_url;
            // $data['remark'] = 'recharge';

            // $signStr = ASCII($data);

            include('SignApi.php');
            // $signStr = 'merchantId='.$mch_id.'&merchantOrderId='.$orderId.'&amount='.$num.'&'.$merchant_key;
            $signAPI = new \SignApi;
	        $sign = $signAPI->sign(ASCII($data), $merchant_key);

	        $data['sign'] = $sign;

            $result = post2($start_url, json_encode($data));
            $repones = json_decode($result, true);
// dump($repones);die;
            if($repones['status'] != '0'){
                return json(['code'=>1,'info'=>$repones['message']]);
            }

            $res = db('xy_recharge')
                ->insert([
                    'id'        => $orderId,
                    'uid'       => $uid,
                    'tel'       => is_null($xy_user['tel']) ? '' : $xy_user['tel'],
                    'real_name' => $xy_user['username'],
                    'pic'       => 'api',
                    'num'       => $num,
                    'addtime'   => time(),
                    'pay_name'  => 'kbpay'
                ]);

            return $this->redirect($repones['data']['pay_url']);
    }

    public function stepay(){
                $num = sprintf('%.2f', input('num/f',0));


        //
        $pay = db('xy_pay')->where('name2','stepay')->find();
         if ($num < $pay['min']) return json(['code'=>1,'info'=>lang('充值不能小于').$pay['min']]);
        if ($num > $pay['max']) return json(['code'=>1,'info'=>lang('充值不能大于').$pay['max']]);

        $uid = cookie('user_id');

        $xy_user = db('xy_users')->find($uid);
            $orderId = getSn('SY');

            $start_url = 'https://pay.stepay.xyz/generate/payment';
            $mch_id = 'aa112233';
            $merchant_key ="d2b474887c9e14f0de71fbf5adf08994";


        //     $mch_id = '12002';
        // 	$merchant_key = 'a5938ded6843fc86568deb452a5da697';


// 参数名	必选	类型	说明
// mch_id	是	string	商户号
// notify_url	是	string	后台通知地址
// page_url	是	string	前台通知地址
// mch_order_no	是	string	商家订单号
// pay_type	是	string	支付类型 查阅商户后台通道编码
// currency	是	string	货币代码 查阅商户后台首页币种
// trade_amount	是	float	交易金额 保留2位小数
// order_date	是	string	订单日期 可以固定一个值 格式 Y-m-d H:i:s 2020-12-12 15:15:15
// goods_name	是	string	商品名称 可以固定一个值
// payer_ip	是	string	客户ip 可以默认 127.0.0.1
// sign	是	string	签名值


        	$page_url = get_http_type(). $_SERVER['SERVER_NAME'];
        	$notify_url = $page_url.'/index/api/stepay_hui';

            $data['mch_id'] = $mch_id;
            $data['pay_type'] = '26';
            $data['mch_order_no'] = $orderId;
            $data['trade_amount'] = $num;
            $data['notify_url'] = $notify_url;
            $data['page_url'] = $page_url;
            $data['currency'] = 'BRL';
            $data['order_date'] = date('Y-m-d H:i:s');
            $data['goods_name'] = 'recharge';
            $data['payer_ip'] = '127.0.0.1';
            // $signStr = ASCII($data);

            include('SignApi.php');
            // $signStr = 'merchantId='.$mch_id.'&merchantOrderId='.$orderId.'&amount='.$num.'&'.$merchant_key;
            $signAPI = new \SignApi;
	        $sign = $signAPI->sign(ASCII($data), $merchant_key);

	        $data['sign'] = $sign;

            $result = post2($start_url, json_encode($data));
            $repones = json_decode($result, true);

            if($repones['code'] != '0'){
                return json(['code'=>1,'info'=>$repones['message']]);
            }

            $res = db('xy_recharge')
                ->insert([
                    'id'        => $orderId,
                    'uid'       => $uid,
                    'tel'       => is_null($xy_user['tel']) ? '' : $xy_user['tel'],
                    'real_name' => $xy_user['username'],
                    'pic'       => 'api',
                    'num'       => $num,
                    'addtime'   => time(),
                    'pay_name'  => 'stepay'
                ]);

            return $this->redirect($repones['data']['pay_url']);
    }

    public function rpay(){
        $num = sprintf('%.2f', input('num/f',0));


        //
        $pay = db('xy_pay')->where('name2','rpay')->find();
         if ($num < $pay['min']) return json(['code'=>1,'info'=>lang('充值不能小于').$pay['min']]);
        if ($num > $pay['max']) return json(['code'=>1,'info'=>lang('充值不能大于').$pay['max']]);

        $uid = cookie('user_id');

        $xy_user = db('xy_users')->find($uid);
            $orderId = getSn('SY');

            $start_url = 'https://rpay.cash/rpay-api/order/submit';
            $mch_id = '20119';
            $merchant_key ="T4ftvcj1OyBomqqA";
        //     $mch_id = '999';
        // 	$merchant_key = 'abc#123!';


        	$page_url = 'https://'. $_SERVER['SERVER_NAME'];
        	$notify_url = $page_url.'/index/api/rpay_hui';

            $data['merchantId'] = $mch_id;
            $data['merchantOrderId'] = $orderId;
            $data['amount'] = $num;
            $data['timestamp'] = msectime();
            $data['payType'] = 1;
            $data['notifyUrl'] = $notify_url;
            $data['callbackUrl'] = $page_url;
            $data['remark'] = 'recharge';

            // $signStr = ASCII($data);

        //     include('SignApi.php');
            $signStr = 'merchantId='.$mch_id.'&merchantOrderId='.$orderId.'&amount='.$num.'&'.$merchant_key;
        //     $signAPI = new \SignApi;
	       // $sign = $signAPI->sign($signStr, $merchant_key);

	        $data['sign'] = md5($signStr);

            $result = post2($start_url, json_encode($data));
            $repones = json_decode($result, true);
// dump($repones);die;
            if($repones['code'] != '0'){
                return json(['code'=>1,'info'=>'error']);
            }

            $res = db('xy_recharge')
                ->insert([
                    'id'        => $orderId,
                    'uid'       => $uid,
                    'tel'       => is_null($xy_user['tel']) ? '' : $xy_user['tel'],
                    'real_name' => $xy_user['username'],
                    'pic'       => 'api',
                    'num'       => $num,
                    'addtime'   => time(),
                    'pay_name'  => 'rpay'
                ]);

            return $this->redirect($repones['data']['h5Url']);
    }

    public function Securepay(){
        $num = sprintf('%.2f', input('num/f',0));


        //
        $pay = db('xy_pay')->where('name2','rpay')->find();
         if ($num < 20) return json(['code'=>1,'info'=>lang('充值不能小于').'20']);
        if ($num > $pay['max']) return json(['code'=>1,'info'=>lang('充值不能大于').$pay['max']]);

        $uid = cookie('user_id');

           $xy_user = db('xy_users')->find($uid);
            $orderId = getSn('SY');


           $merAccount = "9900009"; //商户号
                    $payType = '27001'; //支付通道
                    $payKey = "ec2df171cec5240243b2d26c79ce8dad"; //密钥
                    $dataArr = array(
                        'mer_no' => $merAccount,
                        'order_amount' => $num, //金额
                        'order_no' =>  $orderId, //订单
                        'payemail' => '<EMAIL>',
                        'payphone' => '***********', //印尼OVO业务时,手机号必须时注册OVO预留手机号
                        'currency' => 'PEN', //交易币种
                        'paytypecode' => $payType,//支付类型编码
                        'method' => 'trade.create', //通信类型
                        'payname' => "用户CPFs", //用户名
                        //'returnurl' =>'https://'. $_SERVER['HTTP_HOST'] . '/index/api/rpay_hui22' //回调地址
                        'returnurl' =>'https://'. $_SERVER['HTTP_HOST'] . '/index/api/' //回调地址(不会调)
                    );

                   //  $sign = $signAPI->sign(ASCII($data), $merchant_key);
                //       $signAPI = new \SignApi;
	               //$sign = $signAPI->sign(ASCII($dataArr), $merchant_key);
                    $signStr =
                                "currency=" .$dataArr["currency"].
                                "&mer_no=".$dataArr["mer_no"].
                                "&method=".$dataArr["method"].
                                "&order_amount=".$dataArr["order_amount"].
                                "&order_no=".$dataArr["order_no"].
                                "&payemail=".$dataArr["payemail"].
                                "&payname=".$dataArr["payname"].
                                "&payphone=".$dataArr["payphone"].
                                "&paytypecode=".$dataArr["paytypecode"].
                                "&returnurl=".$dataArr["returnurl"].$payKey;
                    $sign = md5($signStr);

                    $dataArr["sign"] = $sign;
                    $url = "http://www.verysecure2000.com/gateway/";
                    $data = '';

                        try {
                            $ch = curl_init($url);
                            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
                            curl_setopt($ch, CURLOPT_POSTFIELDS,json_encode($dataArr));
                            curl_setopt($ch, CURLOPT_RETURNTRANSFER,true);
                            curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                                'Content-Type: application/json')
                            );

                        // $result = curl_exec($ch);
                            $data = curl_exec($ch);
                            curl_close($ch);
                        } catch (Exception $e) {
                            $data = '';
                        }


            $repones = json_decode($data, true);

            if($repones['status'] != 'success'){
                return json(['code'=>1,'info'=>'error']);
            }

            $res = db('xy_recharge')
                ->insert([
                    'id'        => $orderId,
                    'uid'       => $uid,
                    'tel'       => is_null($xy_user['tel']) ? '' : $xy_user['tel'],
                    'real_name' => $xy_user['username'],
                    'pic'       => 'api',
                    'num'       => $num,
                    'addtime'   => time(),
                    'pay_name'  => 'rpay'
                ]);

            return $this->redirect($repones['order_data']);
    }


    public function kingpay(){
        $num = input('num/f',0);


        //
        $pay = db('xy_pay')->where('name2','kingpay')->find();
         if ($num < $pay['min']) return json(['code'=>1,'info'=>lang('充值不能小于').$pay['min']]);
        if ($num > $pay['max']) return json(['code'=>1,'info'=>lang('充值不能大于').$pay['max']]);

        $uid = cookie('user_id');

        $xy_user = db('xy_users')->find($uid);
            $orderId = getSn('SY');

            $start_url = 'https://interface.sskking.com/pay/web';

            $merchant_key ="TGMVFUGCQ1EZ2PRCZXIBTSMXWNV4HTOJ";

        	$mch_id = '*********';
        	$page_url = 'https://'. $_SERVER['SERVER_NAME'];
        	$notify_url = $page_url.'/index/api/sskking_hui';
        	$mch_order_no = $orderId;
        	$pay_type ='102';
        	$trade_amount = $num;
        	$order_date = date('Y-m-d H:i:s');
        // 	$bank_code = $_POST["bank_code"];
        	$goods_name = 'recharge';
        	$sign_type = 'MD5';
        // 	$mch_return_msg = $_POST["mch_return_msg"];
        	$signStr = "";
        	if($goods_name != ""){
        		$signStr = $signStr."goods_name=".$goods_name."&";
        	}
        	$signStr = $signStr."mch_id=".$mch_id."&";
        	$signStr = $signStr."mch_order_no=".$mch_order_no."&";
        // 	$signStr = $signStr."mch_return_msg=".$mch_return_msg."&";
        	$signStr = $signStr."notify_url=".$notify_url."&";
        	$signStr = $signStr."order_date=".$order_date."&";


        	$signStr = $signStr."page_url=".$page_url."&";
        	$signStr = $signStr."pay_type=".$pay_type."&";
        	$signStr = $signStr."trade_amount=".$trade_amount;
        	$signStr = $signStr."&version=1.0";
            include('SignApi.php');

            $signAPI = new \SignApi;
	        $sign = $signAPI->sign($signStr,$merchant_key);
	        $signStr .=  "&sign_type=".$sign_type;
	        $signStr .= '&sign='.$sign;
	       // goods_name=recharge&mch_id=111222166&mch_order_no=SY2108182055385864&notify_url=https://indeed-2.com/index/user/sskking_hui&order_date=2021-08-18 20:55:38&page_url=https://indeed-1.com&pay_type=102&trade_amount=500&goods_name=recharge&mch_id=111222166&mch_order_no=SY2108182055385864&notify_url=https://indeed-2.com/index/user/sskking_hui&order_date=2021-08-18 20:55:38&page_url=https://indeed-1.com&pay_type=102&trade_amount=500&sign=09af421eabc4d88a754b671338485940

            $result = $signAPI->http_post_res($start_url, $signStr);
            $repones = json_decode($result, true);
            // dump($repones);die;
            if($repones['respCode'] != 'SUCCESS'){
                return json(['code'=>1,'info'=>'error']);
            }

            $res = db('xy_recharge')
                ->insert([
                    'id'        => $orderId,
                    'uid'       => $uid,
                    'tel'       => is_null($xy_user['tel']) ? '' : $xy_user['tel'],
                    'real_name' => $xy_user['username'],
                    'pic'       => 'api',
                    'num'       => $num,
                    'addtime'   => time(),
                    'pay_name'  => 'kingpay'
                ]);
            return $this->redirect($repones['payInfo']);
    }

    public function recharge_do_before()
    {
        $num = input('post.price/f',0);
        $type = input('post.type/s','card');

        $uid = cookie('user_id');
        if(!$num ) return json(['code'=>1,'info'=>lang('参数错误')]);

        //时间限制 //TODO
        $res = check_time(config('chongzhi_time_1'),config('chongzhi_time_2'));
        $str = config('chongzhi_time_1').":00  - ".config('chongzhi_time_2').":00";
        if($res) return json(['code'=>1,'info'=>lang('禁止在').$str.lang('以外的时间段执行当前操作!')]);


        //
        $pay = db('xy_pay')->where('name2',$type)->find();
        if ($num < $pay['min']) return json(['code'=>1,'info'=>lang('充值不能小于').$pay['min']]);
        if ($num > $pay['max']) return json(['code'=>1,'info'=>lang('充值不能大于').$pay['max']]);

           $uid = cookie('user_id');
           $dbs = Db::table('xy_users')->where('id',$uid)->find();
            $ids = getSn('SY');
           $chongzhi_type=config('chongzhi_type');
           if($chongzhi_type==1){
                $uinfo = db('xy_users')->field('pwd,salt,tel,username')->find($uid);

                $res = db('xy_recharge')
                    ->insert([
                        'id'        => $ids,
                        'uid'       => $uid,
                        'tel'       => $uinfo['tel'],
                        'real_name' => $uinfo['username'],
                        'num'       => $num,
                        'status' => 1,
                        'addtime'   => time(),

                    ]);
               $arr=[
                    'mch_id'=>*********, //商户id
                    'mch_order_no'=>$ids, //订单编号
                    'trade_amount'=>$num,//金额
                    'order_date'=>date('Y-m-d H:i:s',time()),//时间
                    'bank_code'=>'IDPT0001',//收款银行代码
                    'pay_type'=>122,
                    "goods_name"=>"充值",
                    'notify_url'=>get_http_type().$_SERVER['SERVER_NAME'].'/index/user/hui2',//回调地址
                    'key'=>'c11a7f310a464d698f9f2ba378b9df4f',
                ];
                $data=$this->ASCII($arr,'sign','key',false,false);
                $data['sign_type']="MD5";
                unset($data['key']);
                $formItemString='';
                $formItemString.="<form style='display:none' name='submit_form' id='submit_form' action='https://pay.sepropay.com/sepro/pay/web' method='post'>";
foreach($data as $key=>$value){
    $formItemString.="<input name='{$key}' type='text' value='{$value}'/>";
}
$formItemString.="</form>";

return json(['code'=>0,'info'=>'Jumping to third party payment','data'=>$formItemString]);
           }else{
               $arr=[
                    "userid"=>"amazon",
                    "orderid"=>$ids,
                    "type"=>"razorpay",
                    "amount"=>$num,
                    "notifyurl"=>get_http_type().$_SERVER['SERVER_NAME'].'/index/user/hui',
                    "returnurl"=>get_http_type().$_SERVER['SERVER_NAME'].'/index/my/index',
                ];
                $arr['sign']=md5("33133a3d-a2e4-43c6-8332-1287219c04cf".$ids.$num);
                $arr=json_encode($arr);
                $data=post2('https://api.zf77777.org/api/create',$arr);
                $data=json_decode($data,true);
                if($data['success']==1){
                    $uinfo = db('xy_users')->field('pwd,salt,tel,username')->find($uid);
                     $res = db('xy_recharge')
                     ->insert([
                         'id'        => $ids,
                         'uid'       => $uid,
                         'tel'       => $uinfo['tel'],
                         'real_name' => $uinfo['username'],
                         'num'       => $num,
                         'status' => 1,
                         'addtime'   => time()
                         ]);
                    return json(['code'=>2,'info'=>'Jumping to third party payment','url'=>$data['pageurl']]);
                }else{
                     return json(['code'=>1,'info'=>'Please contact customer service if payment fails']);
                }
        //     $res =new Payment();
        //     $datas = $res->ds_pay($channelName="UPI",$orderNo= $ids ,$payMoney="$num",$productDetail="1234",$name="$dbs[username]",$email="<EMAIL>"
        //     ,$phone="$dbs[tel]",$redirectUrl=get_http_type().$_SERVER['SERVER_NAME'].'/index/user/hui',$errorReturnUrl="www.baidu.com",$notifyUrl=get_http_type().$_SERVER['SERVER_NAME'].'/index/user/hui');
        //   $jiekuo = post2('http://11128.in:18088/api/international/pay',$datas['post']);
        //   $jieguo = json_decode($jiekuo);

        //   if($jieguo->code == '00'){


        //         $uid = cookie('user_id');
        //         $uinfo = db('xy_users')->field('pwd,salt,tel,username')->find($uid);

        //         $res = db('xy_recharge')
        //             ->insert([
        //                 'id'        => $ids,
        //                 'uid'       => $uid,
        //                 'tel'       => $uinfo['tel'],
        //                 'real_name' => $uinfo['username'],
        //                 'num'       => $num,
        //                 'status' => 1,
        //                 'addtime'   => time(),

        //             ]);
        //      // header("Location: $jieguo->backUrl");
        //       return json(['code'=>2,'info'=>'Jumping to third party payment','url'=>$jieguo->backUrl]);
        //       // die;
        //   }else{
        //          return json(['code'=>1,'info'=>'Please contact customer service if payment fails']);
        //   }
        }

    }



    public function recharge5_do_before()
    {
        $num = input('post.price/f',0);
        $type = input('post.type/s','card');

        $uid = cookie('user_id');
        if(!$num ) return json(['code'=>1,'info'=>lang('参数错误')]);

        //时间限制 //TODO
        $res = check_time(config('chongzhi_time_1'),config('chongzhi_time_2'));
        $str = config('chongzhi_time_1').":00  - ".config('chongzhi_time_2').":00";
        if($res) return json(['code'=>1,'info'=>lang('禁止在').$str.lang('以外的时间段执行当前操作!')]);


        //
        $pay = db('xy_pay')->where('name2',$type)->find();
        if ($num < $pay['min']) return json(['code'=>1,'info'=>lang('充值不能小于').$pay['min']]);
        if ($num > $pay['max']) return json(['code'=>1,'info'=>lang('充值不能大于').$pay['max']]);

        $info = [];
        $info['num'] = $num;
        return json(['code'=>0,'info'=>$info]);
    }
    public function recharge6_do_before()
    {
        $num = input('post.price/f',0);
        $type = input('post.type/s','card');

        $uid = cookie('user_id');
        if(!$num ) return json(['code'=>1,'info'=>lang('参数错误')]);

        //时间限制 //TODO
        $res = check_time(config('chongzhi_time_1'),config('chongzhi_time_2'));
        $str = config('chongzhi_time_1').":00  - ".config('chongzhi_time_2').":00";
        if($res) return json(['code'=>1,'info'=>lang('禁止在').$str.lang('以外的时间段执行当前操作!')]);


        //
        $pay = db('xy_pay')->where('name2',$type)->find();
        if ($num < $pay['min']) return json(['code'=>1,'info'=>lang('充值不能小于').$pay['min']]);
        if ($num > $pay['max']) return json(['code'=>1,'info'=>lang('充值不能大于').$pay['max']]);

        $info = [];
        $info['num'] = $num;
        return json(['code'=>0,'info'=>$info]);
    }

    public function recharge2()
    {
        $oid = input('get.oid/s','');
        $num = input('get.num/s','');
        $type = input('get.type/s','');
        $this->pay = db('xy_pay')->where('status',1)->where('name2',$type)->find();
        if(request()->isPost()) {
            $id = input('post.id/s', '');
            $pic = input('post.pic/s', '');

            if (is_image_base64($pic)) {
                $pic = '/' . $this->upload_base64('xy', $pic);  //调用图片上传的方法
            }else{
                return json(['code'=>1,'info'=>lang('图片格式错误')]);
            }

            $res = db('xy_recharge')->where('id',$id)->update(['pic'=>$pic]);
            if (!$res) {
                return json(['code'=>1,'info'=>lang('提交失败，请稍后再试')]);
            }else{
                return json(['code'=>0,'info'=>lang('请求成功'),'data'=>[]]);
            }
        }


        $info = [];//db('xy_recharge')->find($oid);
        $info['num'] = $num;//db('xy_recharge')->find($oid);

        $this->bank = Db::name('xy_bank')->where('status', 1)->find();

        /*

        */

         $info['master_bank'] = $this->bank['name'];//银行名称config('master_bank')
        $info['master_name'] = $this->bank['realname'];//收款人config('master_name')
        $info['master_cardnum'] = $this->bank['account'];//银行卡号config('master_cardnum')
        $info['master_bk_address'] = $this->bank['kaihuhang'];//银行地址config('master_bk_address')-
        $this->info = $info;

        return $this->fetch();
    }
    public function recharge55()
    {
        $oid = input('get.oid/s','');
        $num = input('get.num/s','');
        $type = input('get.type/s','');
        $this->pay = db('xy_pay')->where('status',1)->where('name2',$type)->find();
        if(request()->isPost()) {
            $id = input('post.id/s', '');
            $pic = input('post.pic/s', '');

            if (is_image_base64($pic)) {
                $pic = '/' . $this->upload_base64('xy', $pic);  //调用图片上传的方法
            }else{
                return json(['code'=>1,'info'=>lang('图片格式错误')]);
            }

            $res = db('xy_recharge')->where('id',$id)->update(['pic'=>$pic]);
            if (!$res) {
                return json(['code'=>1,'info'=>lang('提交失败，请稍后再试')]);
            }else{

                return json(['code'=>0,'info'=>lang('请求成功'),'data'=>[]]);
            }
        }

        $num = $num.'.'.rand(10,99); //随机金额
        $info = [];//db('xy_recharge')->find($oid);
        $info['num'] = $num;//db('xy_recharge')->find($oid);

        $this->bank = Db::name('xy_bank')->where('status', 1)->select();

        /*
        $info['master_bank'] = config('master_bank');//银行名称
        $info['master_name'] = config('master_name');//收款人
        $info['master_cardnum'] = config('master_cardnum');//银行卡号
        $info['master_bk_address'] = config('master_bk_address');//银行地址
        */


        $this->info = $info;

        return $this->fetch();
    }
    public function recharge66()
    {
        $oid = input('get.oid/s','');
        $num = input('get.num/s','');
        $type = input('get.type/s','');
        $this->pay = db('xy_pay')->where('status',1)->where('name2',$type)->find();
        if(request()->isPost()) {
            $id = input('post.id/s', '');
            $pic = input('post.pic/s', '');

            if (is_image_base64($pic)) {
                $pic = '/' . $this->upload_base64('xy', $pic);  //调用图片上传的方法
            }else{
                return json(['code'=>1,'info'=>lang('图片格式错误')]);
            }

            $res = db('xy_recharge')->where('id',$id)->update(['pic'=>$pic]);
            if (!$res) {
                return json(['code'=>1,'info'=>lang('提交失败，请稍后再试')]);
            }else{

                return json(['code'=>0,'info'=>lang('请求成功'),'data'=>[]]);
            }
        }

        $num = $num.'.'.rand(10,99); //随机金额
        $info = [];//db('xy_recharge')->find($oid);
        $info['num'] = $num;//db('xy_recharge')->find($oid);

        $this->bank = Db::name('xy_bank')->where('status', 1)->select();

        /*
        $info['master_bank'] = config('master_bank');//银行名称
        $info['master_name'] = config('master_name');//收款人
        $info['master_cardnum'] = config('master_cardnum');//银行卡号
        $info['master_bk_address'] = config('master_bk_address');//银行地址
        */


        $this->info = $info;

        return $this->fetch();
    }
    //三方支付
    public function recharge3()
    {

        $type = isset($_REQUEST['type']) ? $_REQUEST['type']: 'wx';
        $pay = db('xy_pay')->where('status',1)->select();
        $this->assign('title',$type=='wx' ? lang('微信支付'): lang('支付宝支付'));
        $this->assign('pay',$pay);
        $this->assign('type',$type);
        return $this->fetch();
    }
    public function recharge5(){
        $uid = cookie('user_id');
        $tel = Db::name('xy_users')->where('id',$uid)->value('tel');//获取用户今日已充值金额
        $this->tel = substr_replace($tel,'****',3,4);
        $this->pay = db('xy_pay')->where('status',1)->select();

        return $this->fetch();
    }
    public function recharge6(){
        $uid = cookie('user_id');
        $tel = Db::name('xy_users')->where('id',$uid)->value('tel');//获取用户今日已充值金额
        $this->tel = substr_replace($tel,'****',3,4);
        $this->pay = db('xy_pay')->where('status',1)->select();

        return $this->fetch();
    }
    //钱包页面
    public function bank()
    {
        $balance = db('xy_users')->where('id', cookie('user_id'))->value('balance');
        $this->assign('balance', $balance);
        $balanceT = db('xy_convey')->where('uid', cookie('user_id'))->where('status', 2)->sum('commission');
        $this->assign('balance_shouru', $balanceT);
        return $this->fetch();
    }

    //获取提现订单接口
    public function get_deposit()
    {
        $info = db('xy_deposit')->where('uid',cookie('user_id'))->select();
        if($info) return json(['code'=>0,'info'=>lang('请求成功'),'data'=>$info]);
        return json(['code'=>1,'info'=>lang('暂无数据')]);
    }

    public function my_data()
    {
        $uinfo = db('xy_users')->where('id', cookie('user_id'))->find();
        if ($uinfo['tel']) {
            $uinfo['tel'] = substr_replace($uinfo['tel'], '****', 3, 4);
        }
        $bank = db('xy_bankinfo')->where(['uid'=>cookie('user_id')])->find();
        $uinfo['cardnum'] = substr_replace($bank['cardnum'],'****',7,7);
        if(request()->isPost()) {
            $username = input('post.username/s', '');
            //$pic = input('post.qq/s', '');

            $res = db('xy_users')->where('id',cookie('user_id'))->update(['username'=>$username]);
            if (!$res) {
                return json(['code'=>1,'info'=>lang('提交失败，请稍后再试')]);
            }else{
                return json(['code'=>0,'info'=>lang('请求成功'),'data'=>[]]);
            }
        }

        $this->assign('info', $uinfo);

        return $this->fetch();
    }



    public function recharge_do()
    {
        if(request()->isPost()){
            $num = input('post.price/f',0);
            $type = input('post.type/s','card');
            $pic = input('post.pic/s','');
            $address = input('post.address/s',''); // 添加接收钱包地址参数

            $uid = cookie('user_id');
            $uinfo = db('xy_users')->field('pwd,salt,tel,username')->find($uid);
            if(!$num ) return json(['code'=>1,'info'=>lang('参数错误')]);

            // 修改图片处理部分
            // 检查是否为文件上传还是base64图片
            if(is_file($_FILES['pic']['tmp_name'] ?? '')) {
                // 文件上传方式
                $file = request()->file('pic');
                if($file){
                    $info = $file->validate(['ext'=>'jpg,jpeg,png,gif'])->move('../public/uploads/');
                    if($info){
                        $pic = '/uploads/' . $info->getSaveName();
                    } else {
                        \think\facade\Log::error('图片上传失败: ' . $file->getError());
                        return json(['code'=>1,'info'=>lang('图片上传失败').': '.$file->getError()]);
                    }
                }
            } else if(is_image_base64($pic)) {
                // base64图片处理
                $pic = '/' . $this->upload_base64('xy',$pic);
            } else if(empty($pic)) {
                return json(['code'=>1,'info'=>lang('请上传支付凭证')]);
            } else {
                \think\facade\Log::error('图片格式错误，传入的内容: ' . substr($pic, 0, 100) . '...');
                return json(['code'=>1,'info'=>lang('图片格式错误')]);
            }

            // 记录支付信息和地址
            \think\facade\Log::record('接收充值请求 - 金额:'.$num.' 类型:'.$type.' 地址:'.$address, 'info');

            // 处理加密货币支付类型
            if($type == 'crypto'){
                if(empty($address)){
                    return json(['code'=>1,'info'=>lang('收款地址不能为空')]);
                }

                try {
                    // 查找对应的钱包地址记录
                    $wallet = db('xy_bank')
                        ->where('status', 1)
                        ->where('bank_type', 'Crypto')
                        ->where('account', $address)
                        ->find();

                    if($wallet){
                        $type = $wallet['name']; // 使用钱包名称作为支付类型
                        \think\facade\Log::record('找到对应钱包: '.$wallet['name'], 'info');
                    }else{
                        \think\facade\Log::record('未找到对应钱包，使用默认类型', 'info');
                        $type = 'crypto'; // 如果没找到对应钱包，使用crypto作为默认类型
                    }
                } catch (\Exception $e) {
                    \think\facade\Log::error('查询钱包异常: '.$e->getMessage());
                    $type = 'crypto'; // 发生异常时使用默认类型
                }
            }

            // 验证支付方式和金额
            try {
                $pay = db('xy_pay')->where('name2',$type)->find();
                if(!$pay){
                    \think\facade\Log::record('未找到支付方式，使用默认值', 'info');
                    $pay = ['min'=>0, 'max'=>999999]; // 如果没有找到对应的支付方式，设置默认值
                }

                if ($num < $pay['min']) return json(['code'=>1,'info'=>lang('充值不能小于').$pay['min']]);
                if ($num > $pay['max']) return json(['code'=>1,'info'=>lang('充值不能大于').$pay['max']]);
            } catch (\Exception $e) {
                \think\facade\Log::error('验证支付方式异常: '.$e->getMessage());
                // 异常时不验证金额限制
            }

            try {
                $id = getSn('SY');
                $dd = DB::name('xy_recharge')->where('uid',$uid)->count('*');

                // 准备插入数据
                $insertData = [
                    'id'        => $id,
                    'uid'       => $uid,
                    'tel'       => $uinfo['tel'] ?? '',
                    'real_name' => $uinfo['username'] ?? '',
                    'pic'       => $pic,
                    'num'       => $num,
                    'nums'      => $dd + 1,
                    'addtime'   => time(),
                    'pay_name'  => $type
                ];

                // 检查表结构中是否有address字段
                try {
                    $columns = Db::query('SHOW COLUMNS FROM xy_recharge');
                    $has_address_field = false;
                    foreach ($columns as $column) {
                        if ($column['Field'] == 'address') {
                            $has_address_field = true;
                            break;
                        }
                    }

                    if ($has_address_field) {
                        $insertData['address'] = $address;
                    } else {
                        \think\facade\Log::record('xy_recharge表没有address字段，请先执行update_recharge_table.sql', 'error');
                    }
                } catch (\Exception $e) {
                    \think\facade\Log::error('检查表结构异常: '.$e->getMessage());
                }

                $res = db('xy_recharge')->insert($insertData);

                if($res){
                    $pay['id'] = $id;
                    $pay['num'] = $num;
                    if (isset($pay['name2']) && $pay['name2'] == 'bipay' ) {
                        $pay['redirect'] = url('/index/Api/bipay').'?oid='.$id;
                    }
                    if (isset($pay['name2']) && $pay['name2'] == 'paysapi' ) {
                        $pay['redirect'] = url('/index/Api/pay').'?oid='.$id;
                    }
                    return json(['code'=>0,'info'=>$pay]);
                }
                else
                    return json(['code'=>1,'info'=>lang('提交失败，请稍后再试')]);
            } catch (\Exception $e) {
                \think\facade\Log::error('保存充值记录异常: '.$e->getMessage());
                return json(['code'=>1,'info'=>lang('系统错误，请稍后再试')]);
            }
        }
        return json(['code'=>0,'info'=>lang('请求成功'),'data'=>[]]);
    }

    function deposit_wx(){

        $user = db('xy_users')->where('id', cookie('user_id'))->find();
        $this->assign('title',lang('微信提现'));

        $this->assign('type','wx');
        $this->assign('user',$user);
        return $this->fetch();
    }

    function deposit_simple(){

        $user = db('xy_users')->where('id', cookie('user_id'))->find();
        $this->assign('title',lang('微信提现'));

        $this->assign('type','wx');
        $this->assign('user',$user);
        return $this->fetch();
    }

    function deposit_zfb(){

        $user = db('xy_users')->where('id', cookie('user_id'))->find();
        $this->assign('title',lang('支付宝提现'));

        $this->assign('type','zfb');
        $this->assign('user',$user);
        return $this->fetch('deposit_zfb');
    }


public function withdraw()
    {
        $uid = cookie('user_id');

        // 检查实名认证状态
        $user_info = Db::name('xy_users')->where('id', $uid)->find();
        if ($user_info['id_status'] != 1) {
            $this->error(lang('Please complete identity verification before withdrawing'), url('my/identity_verify'));
        }

        // 获取用户数据
        $userData = Db::name('xy_users')->field('id,username,tel,balance')->find($uid);

        // 确保userData数据安全
        if (!isset($userData['balance'])) {
            $userData['balance'] = 0;
        }

        $this->assign('userData', $userData);

        if($_POST){
            $res = check_time(config('tixian_time_1'),config('tixian_time_2'));
            $str = config('tixian_time_1').":00  - ".config('tixian_time_2').":00";
            if($res) return json(['code'=>1,'info'=>lang('禁止在').$str.lang('以外的时间段执行当前操作!'),'a'=>1]);
        }

        return $this->fetch();
    }

    //提现接口
    public function do_deposit()
    {
        // 设置更长的执行时间，避免超时
        @set_time_limit(60);

        $uid = cookie('user_id');
        if(!$uid) {
            return json(['code'=>1, 'info'=>lang('请先登录'), 'a'=>1]);
        }

        // 提现时间限制检查
        $res = check_time(config('tixian_time_1'), config('tixian_time_2'));
        if($res) {
            $str = config('tixian_time_1').":00 - ".config('tixian_time_2').":00";
            return json(['code'=>1, 'info'=>lang('禁止在').$str.lang('以外的时间段执行当前操作!'), 'a'=>1]);
        }

        // 实名认证检查
        $user_info = Db::name('xy_users')->where('id', $uid)->find();
        if(!$user_info || $user_info['id_status'] != 1) {
            return json(['code'=>1, 'info'=>lang('Please complete identity verification before withdrawing'), 'a'=>1]);
        }

        if(request()->isPost()) {
            // 交易密码验证
            $pwd2 = input('post.paypassword/s', '');
            if(empty($pwd2)) {
                $pwd2 = input('post.pwd/s', '');
            }

            $info = Db::name('xy_users')->field('pwd2,salt2')->find($uid);
            if(empty($info['pwd2'])) {
                return json(['code'=>1, 'info'=>lang('未设置交易密码'), 'a'=>0]);
            }

            // 验证密码
            $expected_pwd = sha1($pwd2.$info['salt2'].config('pwd_str'));
            if($info['pwd2'] != $expected_pwd) {
                return json(['code'=>1, 'info'=>lang('密码错误'), 'a'=>0]);
            }

            // 处理提现金额
            $num = input('post.num/d', 0);
            if($num <= 0) {
                $num = input('post.money/d', 0);
            }

            if($num <= 0) {
                return json(['code'=>1, 'info'=>lang('提现金额必须大于0'), 'a'=>0]);
            }

            // 获取用户信息
            $uinfo = Db::name('xy_users')->field('balance,level')->find($uid);
            if(!isset($uinfo['balance']) || $num > $uinfo['balance']) {
                return json(['code'=>1, 'info'=>lang('余额不足'), 'a'=>0]);
            }

            // 获取用户等级信息
            $level = isset($uinfo['level']) ? $uinfo['level'] : 0;
            $ulevel = Db::name('xy_level')->where('level', $level)->find();

            // 检查提现额度限制
            if(isset($ulevel['tixian_min']) && $num < $ulevel['tixian_min']) {
                return json(['code'=>1, 'info'=>lang('最低提现金额为').$ulevel['tixian_min'], 'a'=>0]);
            }

            if(isset($ulevel['tixian_max']) && $num >= $ulevel['tixian_max']) {
                return json(['code'=>1, 'info'=>lang('最高提现金额为').$ulevel['tixian_max'], 'a'=>0]);
            }

            try {
                // 获取银行信息
                $bankinfo = Db::name('xy_bankinfo')->where('uid', $uid)->where('status', 1)->find();

                Db::startTrans();

                // 生成唯一订单号
                $id = getSn('CO');
                $type = config('tixian_type');
                $dd = Db::name('xy_deposit')->where('uid', $uid)->count('*');

                // 计算手续费和实际到账金额
                $shouxu = isset($ulevel['tixian_shouxu']) ? $ulevel['tixian_shouxu'] : 0;
                $real_num = $num - $shouxu;

                // 插入提现记录
                $bkid = input('post.bk_id/d', isset($bankinfo['id']) ? $bankinfo['id'] : 0);
                $res = Db::name('xy_deposit')->insert([
                    'id'       => $id,
                    'uid'      => $uid,
                    'bk_id'    => $bkid,
                    'num'      => $num,
                    'nums'     => $dd + 1,
                    'addtime'  => time(),
                    'type'     => $type,
                    'shouxu'   => $shouxu,
                    'real_num' => $real_num,
                    'status'   => 0
                ]);

                // 添加余额变动记录
                $res2 = Db::name('xy_balance_log')->insert([
                    'uid'     => $uid,
                    'oid'     => $id,
                    'num'     => $num,
                    'type'    => 7,
                    'status'  => 0,
                    'addtime' => time()
                ]);

                // 扣除用户余额
                $res1 = Db::name('xy_users')->where('id', $uid)->setDec('balance', $num);

                if($res && $res1 && $res2) {
                    Db::commit();
                    return json(['code'=>0, 'info'=>lang('操作成功'), 'a'=>0]);
                } else {
                    Db::rollback();
                    return json(['code'=>1, 'info'=>lang('操作失败'), 'a'=>0]);
                }
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code'=>1, 'info'=>lang('系统错误，请稍后再试'), 'a'=>0]);
            }
        }

        $bankinfo = Db::name('xy_bankinfo')->where('uid', $uid)->where('status', 1)->find();
        return json(['code'=>0, 'info'=>lang('请求成功'), 'data'=>$bankinfo, 'a'=>1]);
    }

    //////get请求获取参数，post请求写入数据，post请求传人bkid则更新数据//////////
    public function do_bankinfo()
    {
        if(request()->isPost()){
            $token = input('post.token','');
            $data = ['__token__' => $token];
            $validate   = \Validate::make($this->rule,$this->msg);
            if(!$validate->check($data)) return json(['code'=>1,'info'=>$validate->getError()]);

            $username = input('post.username/s','');
            $bankname = input('post.bankname/s','');
            $cardnum = input('post.cardnum/s','');
            $site = input('post.site/s','');
            $tel = input('post.tel/s','');
            $status = input('post.default/d',0);
            $bkid = input('post.bkid/d',0); //是否为更新数据

            if(!$username)return json(['code'=>1,'info'=>lang('开户人名称为必填项')]);
            if(mb_strlen($username) > 30)return json(['code'=>1,'info'=>lang('开户人名称长度最大为30个字符')]);
            if(!$bankname)return json(['code'=>1,'info'=>lang('银行名称为必填项')]);
            if(!$cardnum)return json(['code'=>1,'info'=>lang('银行卡号为必填项')]);
            if(!$tel)return json(['code'=>1,'info'=>lang('手机号为必填项')]);

            if($bkid)
                $cardn = Db::table('xy_bankinfo')->where('id','<>',$bkid)->where('cardnum',$cardnum)->count();
            else
                $cardn = Db::table('xy_bankinfo')->where('cardnum',$cardnum)->count();

            if($cardn)return json(['code'=>1,'info'=>lang('银行卡号已存在')]);

            $data = ['uid'=>cookie('user_id'),'bankname'=>$bankname,'cardnum'=>$cardnum,'tel'=>$tel,'site'=>$site,'username'=>$username];
            if($status){
                Db::table('xy_bankinfo')->where(['uid'=>cookie('user_id')])->update(['status'=>0]);
                $data['status'] = 1;
            }

            if($bkid)
                $res = Db::table('xy_bankinfo')->where('id',$bkid)->where('uid',cookie('user_id'))->update($data);
            else
                $res = Db::table('xy_bankinfo')->insert($data);

            if($res!==false)
                return json(['code'=>0,'info'=>lang('操作成功')]);
            else
                return json(['code'=>1,'info'=>lang('操作失败')]);
        }
        $bkid = input('id/d',0); //是否为更新数据
        $where = ['uid'=>cookie('user_id')];
        if($bkid!==0) $where['id'] = $bkid;
        $info = db('xy_bankinfo')->where($where)->select();
        if(!$info) return json(['code'=>1,'info'=>lang('暂无数据')]);
        return json(['code'=>0,'info'=>lang('请求成功'),'data'=>$info]);
    }

    //切换银行卡状态
    public function edit_bankinfo_status()
    {
        $id = input('post.id/d',0);

        Db::table('bankinfo')->where(['uid'=>cookie('user_id')])->update(['status'=>0]);
        $res = Db::table('bankinfo')->where(['id'=>$id,'uid'=>cookie('user_id')])->update(['status'=>1]);
        if($res !== false)
            return json(['code'=>0,'info'=>lang('操作成功')]);
        else
            return json(['code'=>1,'info'=>lang('操作失败')]);
    }

    //获取下级会员
    public function bot_user()
    {
        if(request()->isPost()){
            $uid = input('post.id/d',0);
            $token = ['__token__' => input('post.token','')];
            $validate   = \Validate::make($this->rule,$this->msg);
            if(!$validate->check($token)) return json(['code'=>1,'info'=>$validate->getError()]);
        }else{
            $uid = cookie('user_id');
        }
        $page = input('page/d',1);
        $num = input('num/d',10);
        $limit = ( (($page - 1) * $num) . ',' . $num );
        $data = db('xy_users')->where('parent_id',$uid)->field('id,username,headpic,addtime,childs,tel')->limit($limit)->order('addtime desc')->select();
        if(!$data) return json(['code'=>1,'info'=>lang('暂无数据')]);
        return json(['code'=>0,'info'=>lang('请求成功'),'data'=>$data]);
    }

    //修改密码
    public function set_pwd()
    {
        if(!request()->isPost()) return json(['code'=>1,'info'=>lang('错误请求')]);
        $o_pwd = input('old_pwd/s','');
        $pwd = input('new_pwd/s','');
        $type = input('type/d',1);
        $uinfo = db('xy_users')->field('pwd,pwd2,salt,salt2,tel')->find(cookie('user_id'));
        // $data['pwd2'] = sha1($pwd2.$salt2.config('pwd_str'));
        if($uinfo['pwd2']!=sha1($o_pwd.$uinfo['salt2'].config('pwd_str'))) return json(['code'=>1,'info'=>lang('密码错误')]);
        $res = model('admin/Users')->reset_pwd($uinfo['tel'],$pwd,$type);
        return json($res);
    }

    public function set()
    {
        $uid = cookie('user_id');
        $this->info = db('xy_users')->find($uid);
        $this->info['tel'] = substr($this->info['tel'], 0, 3) . '****' . substr($this->info['tel'], 7, 10);
        return $this->fetch();
    }



    //我的下级
    public function get_user()
    {

        $uid = cookie('user_id');

        $type = input('post.type/d',1);

        $page = input('page/d',1);
        $num = input('num/d',10);
        $limit = ( (($page - 1) * $num) . ',' . $num );
        $uinfo = db('xy_users')->field('*')->find(cookie('user_id'));
        $other = [];
        if ($type == 1) {
            $uid = cookie('user_id');
            $data = db('xy_users')->where('parent_id', $uid)
                ->field('id,username,headpic,addtime,childs,tel')
                ->limit($limit)
                ->order('addtime desc')
                ->select();

            //总的收入  总的充值
            //$ids1 = db('xy_users')->where('parent_id', $uid)->field('id')->column('id');
            //$cond=implode(',',$ids1);
            //$cond = !empty($cond) ? $cond = " uid in ($cond)":' uid=-1';
            $other = [];
            //$other['chongzhi'] = db('xy_recharge')->where($cond)->where('status', 2)->sum('num');
            //$other['tixian'] = db('xy_deposit')->where($cond)->where('status', 2)->sum('num');
            //$other['xiaji'] = count($ids1);

            $uids = model('admin/Users')->child_user($uid,5);
            $uids ? $where[] = ['uid','in',$uids] : $where[] = ['uid','in',[-1]];
            $uids ? $where2[] = ['uid','in',$uids] : $where2[] = ['uid','in',[-1]];

            $other['chongzhi'] = db('xy_recharge')->where($where2)->where('status', 2)->sum('num');
            $other['tixian'] = db('xy_deposit')->where($where2)->where('status', 2)->sum('num');
            $other['xiaji'] = count($uids);


            //var_dump($uinfo);die;

            $iskou =0;
            foreach ($data as &$datum) {
                $datum['addtime'] = date('Y/m/d H:i', $datum['addtime']);
                empty($datum['headpic']) ? $datum['headpic'] = '/public/img/head.png':'';
                //充值
                $datum['chongzhi'] = db('xy_recharge')->where('uid', $datum['id'])->where('status', 2)->sum('num');
                //提现
                $datum['tixian'] = db('xy_deposit')->where('uid', $datum['id'])->where('status', 2)->sum('num');

                if ($uinfo['kouchu_balance_uid'] == $datum['id']) {
                    $datum['chongzhi'] -= $uinfo['kouchu_balance'];
                    $iskou = 1;
                }

                if ($uinfo['show_tel2']) {
                    $datum['tel'] = substr_replace($datum['tel'], '****', 3, 4);
                }
                if (!$uinfo['show_tel']) {
                    $datum['tel'] = lang('无权限');
                }
                if (!$uinfo['show_num']) {
                    $datum['childs'] = lang('无权限');
                }
                if (!$uinfo['show_cz']) {
                    $datum['chongzhi'] = lang('无权限');
                }
                if (!$uinfo['show_tx']) {
                    $datum['tixian'] = lang('无权限');
                }
            }

            $other['chongzhi'] -= $uinfo['kouchu_balance'];
            return json(['code'=>0,'info'=>lang('请求成功'),'data'=>$data,'other'=>$other]);

        }else if($type == 2) {
            $ids1 = db('xy_users')->where('parent_id', $uid)->field('id')->column('id');
            $cond=implode(',',$ids1);
            $cond = !empty($cond) ? $cond = " parent_id in ($cond)":' parent_id=-1';

            //获取二代ids
            $ids2 = db('xy_users')->where($cond)->field('id')->column('id');
            $cond2=implode(',',$ids2);
            $cond2 = !empty($cond2) ? $cond2 = " uid in ($cond2)":' uid=-1';
            $other = [];
            $other['chongzhi'] = db('xy_recharge')->where($cond2)->where('status', 2)->sum('num');
            $other['tixian'] = db('xy_deposit')->where($cond2)->where('status', 2)->sum('num');
            $other['xiaji'] = count($ids2);



            $data = db('xy_users')->where($cond)
                ->field('id,username,headpic,addtime,childs,tel')
                ->limit($limit)
                ->order('addtime desc')
                ->select();

            //总的收入  总的充值

            foreach ($data as &$datum) {
                empty($datum['headpic']) ? $datum['headpic'] = '/public/img/head.png':'';
                $datum['addtime'] = date('Y/m/d H:i', $datum['addtime']);
                //充值
                $datum['chongzhi'] = db('xy_recharge')->where('uid', $datum['id'])->where('status', 2)->sum('num');
                //提现
                $datum['tixian'] = db('xy_deposit')->where('uid', $datum['id'])->where('status', 2)->sum('num');

                if ($uinfo['show_tel2']) {
                    $datum['tel'] = substr_replace($datum['tel'], '****', 3, 4);
                }
                if (!$uinfo['show_tel']) {
                    $datum['tel'] = lang('无权限');
                }
                if (!$uinfo['show_num']) {
                    $datum['childs'] = lang('无权限');
                }
                if (!$uinfo['show_cz']) {
                    $datum['chongzhi'] =  lang('无权限');
                }
                if (!$uinfo['show_tx']) {
                    $datum['tixian'] =  lang('无权限');
                }
            }

            return json(['code'=>0,'info'=>lang('请求成功'),'data'=>$data,'other'=>$other]);


        }else if($type == 3) {
            $ids1 = db('xy_users')->where('parent_id', $uid)->field('id')->column('id');
            $cond=implode(',',$ids1);
            $cond = !empty($cond) ? $cond = " parent_id in ($cond)":' parent_id=-1';
            $ids2 = db('xy_users')->where($cond)->field('id')->column('id');

            $cond2=implode(',',$ids2);
            $cond2 = !empty($cond2) ? $cond2 = " parent_id in ($cond2)":' parent_id=-1';

            //获取三代的ids
            $ids22 = db('xy_users')->where($cond2)->field('id')->column('id');
            $cond22=implode(',',$ids22);
            $cond22 = !empty($cond22) ? $cond22 = " uid in ($cond22)":' uid=-1';
            $other = [];
            $other['chongzhi'] = db('xy_recharge')->where($cond22)->where('status', 2)->sum('num');
            $other['tixian'] = db('xy_deposit')->where($cond22)->where('status', 2)->sum('num');
            $other['xiaji'] = count($ids22);

            //获取四代ids
            $cond4 =implode(',',$ids22);
            $cond4 = !empty($cond4) ? $cond4 = " parent_id in ($cond4)":' parent_id=-1';
            $ids4  = db('xy_users')->where($cond4)->field('id')->column('id'); //四代ids

            //充值
            $cond44 =implode(',',$ids4);
            $cond44 = !empty($cond44) ? $cond44 = " uid in ($cond44)":' uid=-1';
            $other['chongzhi4'] = db('xy_recharge')->where($cond44)->where('status', 2)->sum('num');
            $other['tixian4'] = db('xy_deposit')->where($cond44)->where('status', 2)->sum('num');
            $other['xiaji4'] = count($ids4);



            //获取五代
            $cond5 = implode(',',$ids4);
            $cond5 = !empty($cond5) ? $cond5 = " parent_id in ($cond5)":' parent_id=-1';
            $ids5  = db('xy_users')->where($cond5)->field('id')->column('id'); //五代ids

            //充值
            $cond55 =implode(',',$ids5);
            $cond55 = !empty($cond55) ? $cond55 = " uid in ($cond55)":' uid=-1';
            $other['chongzhi5'] = db('xy_recharge')->where($cond55)->where('status', 2)->sum('num');
            $other['tixian5'] = db('xy_deposit')->where($cond55)->where('status', 2)->sum('num');
            $other['xiaji5'] = count($ids5);

            $other['chongzhi_all'] = $other['chongzhi'] + $other['chongzhi4']+ $other['chongzhi5'];
            $other['tixian_all']   = $other['tixian'] + $other['tixian4']+ $other['tixian5'];

            $data = db('xy_users')->where($cond2)
                ->field('id,username,headpic,addtime,childs,tel')
                ->limit($limit)
                ->order('addtime desc')
                ->select();

            //总的收入  总的充值

            foreach ($data as &$datum) {
                $datum['addtime'] = date('Y/m/d H:i', $datum['addtime']);
                empty($datum['headpic']) ? $datum['headpic'] = '/public/img/head.png':'';
                //充值
                $datum['chongzhi'] = db('xy_recharge')->where('uid', $datum['id'])->where('status', 2)->sum('num');
                //提现
                $datum['tixian'] = db('xy_deposit')->where('uid', $datum['id'])->where('status', 2)->sum('num');

                if ($uinfo['show_tel2']) {
                    $datum['tel'] = substr_replace($datum['tel'], '****', 3, 4);
                }
                if (!$uinfo['show_tel']) {
                    $datum['tel'] =  lang('无权限');
                }
                if (!$uinfo['show_num']) {
                    $datum['childs'] =  lang('无权限');
                }
                if (!$uinfo['show_cz']) {
                    $datum['chongzhi'] =  lang('无权限');
                }
                if (!$uinfo['show_tx']) {
                    $datum['tixian'] =  lang('无权限');
                }
            }
            return json(['code'=>0,'info'=>lang('请求成功'),'data'=>$data,'other'=>$other]);
        }



        return json(['code'=>0,'info'=>lang('请求成功'),'data'=>[]]);
    }



    /**
     * 提现记录
     */
    public function deposit_admin()
    {
        $id = cookie('user_id');
        $where=[];
        $this->_query('xy_deposit')
            ->where('uid',$id)->where($where)->order('id desc')->page();

    }


    /**
     * 团队
     */
    public function junior()
    {
        // 改进的用户ID获取逻辑
        $uid = session('user_id');
        if (!$uid) {
            $uid = cookie('user_id');
        }
        if (!$uid) {
            return $this->redirect('User/login');
        }

        try {
            $where = [];
            $this->level = $level = input('get.level/d', 1);
            $this->uinfo = db('xy_users')->where('id', $uid)->find();

            if (!$this->uinfo) {
                $this->error('用户信息不存在');
            }

            // 检查模型是否存在
            if (!class_exists('app\\admin\\model\\Users')) {
                // 如果模型不存在，使用简化版本
                $this->assign('level', $level);
                $this->assign('uinfo', $this->uinfo);
                $this->assign('teamyue', 0);
                $this->assign('teamcz', 0);
                $this->assign('teamtx', 0);
                $this->assign('teamls', 0);
                $this->assign('teamyj', 0);
                $this->assign('zhitui', 0);
                $this->assign('tuandui', 0);
                $this->assign('start', '');
                $this->assign('end', '');

                // 简单的用户列表查询
                $list = db('xy_users')
                    ->where('pid', $uid)
                    ->order('id desc')
                    ->paginate(10, false, [
                        'query' => request()->param()
                    ]);

                $this->assign('list', $list);
                $this->assign('pagehtml', $list->render());

                return $this->fetch();
            }

            // 计算五级团队余额
            $uidAlls5 = model('admin/Users')->child_user($uid, 5, 1);
            $uidAlls5 ? $whereAll[] = ['id', 'in', $uidAlls5] : $whereAll[] = ['id', 'in', [-1]];
            $uidAlls5 ? $whereAll2[] = ['uid', 'in', $uidAlls5] : $whereAll2[] = ['uid', 'in', [-1]];

            $this->teamyue = db('xy_users')->where($whereAll)->sum('balance');
            $this->teamcz = db('xy_recharge')->where($whereAll2)->where('status', 2)->sum('num');
            $this->teamtx = db('xy_deposit')->where($whereAll2)->where('status', 2)->sum('num');
            $this->teamls = db('xy_balance_log')->where($whereAll2)->sum('num');
            $this->teamyj = db('xy_convey')->where('status', 1)->where($whereAll2)->sum('commission');

            $uids1 = model('admin/Users')->child_user($uid, 1, 0);
            $this->zhitui = count($uids1);
            $uidsAll = model('admin/Users')->child_user($uid, 5, 1);
            $this->tuandui = count($uidsAll);

            // 处理时间筛选
            $start = input('get.start/s', '');
            $end = input('get.end/s', '');
            if ($start || $end) {
                $start ? $start = strtotime($start) : $start = strtotime('2020-01-01');
                $end ? $end = strtotime($end . ' 23:59:59') : $end = time();
                $where[] = ['addtime', 'between', [$start, $end]];
            }
            $this->start = $start ? date('Y-m-d', $start) : '';
            $this->end = $end ? date('Y-m-d', $end) : '';

            // 获取指定级别的下级用户
            $uids5 = model('admin/Users')->child_user($uid, $level, 0);
            $uids5 ? $where[] = ['id', 'in', $uids5] : $where[] = ['id', 'in', [-1]];

            // 修复：使用标准的分页查询替代_query方法
            $list = db('xy_users')
                ->where($where)
                ->order('id desc')
                ->paginate(10, false, [
                    'query' => request()->param()
                ]);

            // 设置模板变量
            $this->assign('level', $level);
            $this->assign('uinfo', $this->uinfo);
            $this->assign('teamyue', $this->teamyue);
            $this->assign('teamcz', $this->teamcz);
            $this->assign('teamtx', $this->teamtx);
            $this->assign('teamls', $this->teamls);
            $this->assign('teamyj', $this->teamyj);
            $this->assign('zhitui', $this->zhitui);
            $this->assign('tuandui', $this->tuandui);
            $this->assign('start', $this->start);
            $this->assign('end', $this->end);
            $this->assign('list', $list);
            $this->assign('pagehtml', $list->render());

            return $this->fetch();

        } catch (\Exception $e) {
            // 记录错误日志
            \think\facade\Log::error('团队页面错误: ' . $e->getMessage());

            // 返回简化版本
            $this->assign('level', 1);
            $this->assign('uinfo', $this->uinfo ?? []);
            $this->assign('teamyue', 0);
            $this->assign('teamcz', 0);
            $this->assign('teamtx', 0);
            $this->assign('teamls', 0);
            $this->assign('teamyj', 0);
            $this->assign('zhitui', 0);
            $this->assign('tuandui', 0);
            $this->assign('start', '');
            $this->assign('end', '');

            // 简单的用户列表查询 - 修复字段名
            $list = db('xy_users')
                ->where('parent_id', $uid)
                ->order('id desc')
                ->paginate(10, false, [
                    'query' => request()->param()
                ]);

            $this->assign('list', $list);
            $this->assign('pagehtml', $list->render());

            return $this->fetch();
        }
    }

    public function ASCII($asciiData, $asciiSign = 'sign', $asciiKey = 'key', $asciiSize = true, $asciiKeyBool = false)
    {
        //编码数组从小到大排序
        ksort($asciiData);
        //拼接源文->签名是否包含密钥->密钥最后拼接
        $MD5str = "";
        foreach ($asciiData as $key => $val) {
            if (!$asciiKeyBool && $asciiKey == $key) continue;
            $MD5str .= $key . "=" . $val . "&";
        }
        $sign = $MD5str . $asciiKey . "=" . $asciiData[$asciiKey];
        //大小写->md5
        $asciiData[$asciiSign]  = $asciiSize ? strtoupper(md5($sign)) : strtolower(md5($sign));
        return $asciiData;
    }

    /**
     * 充值记录
     */
    public function recharge()
    {
        // 验证用户是否登录
        $uid = cookie('user_id');
        if(!$uid){
            return $this->redirect('User/login');
        }

        // 从数据库获取钱包地址
        $wallets = [];

        try {
            // 尝试从数据库获取钱包
            $db_wallets = db('xy_bank')
                ->where('status', 1)
                ->where('bank_type', 'Crypto')
                ->field('id,name,account')
                ->select();

            if(!empty($db_wallets)) {
                $wallets = $db_wallets;
            }
        } catch (\Exception $e) {
            // 查询失败时不抛出异常，使用默认值
        }

        // 如果数据库查询失败，使用默认钱包配置
        if(empty($wallets)) {
            $wallets = [
                [
                    'id' => 1,
                    'name' => 'BTC-USDT',
                    'account' => 't66yyuuiiiiitutututut'
                ],
                [
                    'id' => 2,
                    'name' => 'ERC20-USDT',
                    'account' => 'ggtttyuuiiiuuyyyyttt'
                ],
                [
                    'id' => 3,
                    'name' => 'BNB-USDT',
                    'account' => 'erttyuuuiihhhhggttttt'
                ],
                [
                    'id' => 4,
                    'name' => 'TRC20-USDT',
                    'account' => 'qssddrfffggh'
                ]
            ];
        }

        // 检测用户选择的语言
        $lang = 'en'; // 默认使用英文

        // 如果需要从cookie或请求参数获取语言设置
        if(cookie('think_lang')) {
            $lang = cookie('think_lang');
        }

        // 允许通过URL参数覆盖语言设置
        if(input('lang')) {
            $lang = input('lang');
        }

        // 多语言文本
        $i18n = [
            'zh-cn' => [
                'title' => '充值',
                'amount' => '充值金额',
                'amount_placeholder' => '请输入充值金额',
                'payment_type' => '选择支付类型',
                'payment_type_placeholder' => '请选择支付类型',
                'copy_address' => '复制地址',
                'scan_qrcode' => '扫描二维码获取收款地址',
                'upload_title' => '上传支付截图',
                'select_file' => '点击选择文件',
                'submit' => '提交充值',
                'back' => '« 返回首页',
                'copied' => '地址已复制到剪贴板',
                'amount_error' => '请输入有效金额',
                'type_error' => '请选择支付类型',
                'upload_error' => '请上传支付截图',
                'processing' => '处理中...',
                'success' => '充值提交成功，等待审核',
                'fail' => '提交失败，请稍后再试',
                'network_error' => '网络错误，请稍后再试',
                'step1' => '填写信息',
                'step2' => '转账支付',
                'step3' => '上传凭证'
            ],
            'en' => [
                'title' => 'Recharge',
                'amount' => 'Amount',
                'amount_placeholder' => 'Enter recharge amount',
                'payment_type' => 'Payment Method',
                'payment_type_placeholder' => 'Select payment method',
                'copy_address' => 'Copy',
                'scan_qrcode' => 'Scan QR code for payment address',
                'upload_title' => 'Upload Payment Screenshot',
                'select_file' => 'Select file',
                'submit' => 'Submit',
                'back' => '« Back to home',
                'copied' => 'Address copied to clipboard',
                'amount_error' => 'Please enter a valid amount',
                'type_error' => 'Please select a payment method',
                'upload_error' => 'Please upload payment screenshot',
                'processing' => 'Processing...',
                'success' => 'Submission successful, awaiting approval',
                'fail' => 'Submission failed, please try again',
                'network_error' => 'Network error, please try again',
                'step1' => 'Details',
                'step2' => 'Payment',
                'step3' => 'Proof'
            ]
        ];

        // 如果不支持请求的语言，使用英文作为后备
        if(!isset($i18n[$lang])) {
            $lang = 'en';
        }

        // 根据检测的语言选择文本
        $text = $i18n[$lang];

        $html = '<!DOCTYPE html>
        <html lang="'.$lang.'">
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
            <title>'.$text['title'].'</title>
            <style>
                * {
                    box-sizing: border-box;
                    margin: 0;
                    padding: 0;
                }

                body {
                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif;
                    background-color: #f7f9fc;
                    color: #333;
                    line-height: 1.6;
                    padding: 20px;
                }

                .container {
                    max-width: 600px;
                    margin: 0 auto;
                    background: #fff;
                    border-radius: 12px;
                    padding: 30px;
                    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
                }

                h1 {
                    color: #3f51b5;
                    font-size: 28px;
                    margin-bottom: 25px;
                    text-align: center;
                    font-weight: 700;
                }

                .wallet-item {
                    border: 1px solid #e0e0e0;
                    padding: 25px;
                    margin-bottom: 20px;
                    border-radius: 12px;
                    display: none;
                    background: #fff;
                }

                .wallet-name {
                    font-weight: 700;
                    margin-bottom: 15px;
                    font-size: 20px;
                    color: #3f51b5;
                    border-bottom: 2px solid #f0f0f0;
                    padding-bottom: 10px;
                }

                .wallet-address {
                    word-break: break-all;
                    background: #f8f9fa;
                    padding: 15px;
                    border-radius: 8px;
                    margin: 15px 0;
                    border: 1px dashed #e0e0e0;
                    font-family: monospace;
                    font-size: 14px;
                }

                .address-container {
                    display: flex;
                    align-items: center;
                }

                .copy-btn {
                    background: #3f51b5;
                    color: white;
                    border: none;
                    padding: 8px 15px;
                    border-radius: 6px;
                    cursor: pointer;
                    font-weight: 500;
                    margin-left: 10px;
                }

                .qrcode {
                    text-align: center;
                    margin: 20px 0;
                    padding: 15px;
                    background: #f8f9fa;
                    border-radius: 8px;
                }

                .qrcode img {
                    border: 8px solid white;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }

                .input-group {
                    margin: 20px 0;
                }

                label {
                    display: block;
                    margin-bottom: 8px;
                    font-weight: 500;
                    color: #555;
                }

                input, select {
                    width: 100%;
                    padding: 15px;
                    border: 1px solid #e0e0e0;
                    border-radius: 8px;
                    background: #f8f9fa;
                    font-size: 16px;
                }

                .upload-section {
                    margin-top: 30px;
                    border-top: 1px solid #e0e0e0;
                    padding-top: 25px;
                }

                .upload-section h3 {
                    margin-bottom: 15px;
                    color: #3f51b5;
                    font-size: 18px;
                }

                .file-input-container {
                    position: relative;
                    margin-bottom: 20px;
                }

                .file-input-label {
                    display: block;
                    padding: 15px;
                    text-align: center;
                    background: #f0f2f5;
                    border: 2px dashed #ccc;
                    border-radius: 8px;
                    cursor: pointer;
                }

                input[type="file"] {
                    position: absolute;
                    width: 0;
                    height: 0;
                    opacity: 0;
                }

                #preview {
                    text-align: center;
                    margin: 20px 0;
                }

                #preview-image {
                    max-width: 100%;
                    max-height: 300px;
                    border-radius: 8px;
                    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                }

                .submit-btn {
                    display: block;
                    width: 100%;
                    padding: 16px;
                    background: #3f51b5;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    font-size: 18px;
                    font-weight: 500;
                    cursor: pointer;
                    text-align: center;
                }

                .back-btn {
                    display: inline-block;
                    margin-top: 25px;
                    color: #3f51b5;
                    text-decoration: none;
                    font-weight: 500;
                }

                .step-progress {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 30px;
                }

                .step {
                    flex: 1;
                    text-align: center;
                    position: relative;
                }

                .step-number {
                    width: 30px;
                    height: 30px;
                    border-radius: 50%;
                    background: #e0e0e0;
                    color: #666;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 0 auto 10px;
                    font-weight: bold;
                    position: relative;
                    z-index: 2;
                }

                .step.active .step-number {
                    background: #3f51b5;
                    color: white;
                }

                .step-title {
                    font-size: 14px;
                    color: #666;
                }

                .step.active .step-title {
                    color: #3f51b5;
                    font-weight: 500;
                }

                .step-line {
                    position: absolute;
                    top: 15px;
                    left: 0;
                    right: 0;
                    height: 2px;
                    background: #e0e0e0;
                    z-index: 1;
                }

                .step:first-child .step-line {
                    left: 50%;
                }

                .step:last-child .step-line {
                    right: 50%;
                }

                .toast {
                    position: fixed;
                    bottom: 30px;
                    left: 50%;
                    transform: translateX(-50%);
                    background: rgba(0, 0, 0, 0.8);
                    color: white;
                    padding: 12px 24px;
                    border-radius: 30px;
                    font-size: 14px;
                    display: none;
                    z-index: 1000;
                }

                @media (max-width: 480px) {
                    .container {
                        padding: 20px 15px;
                    }

                    h1 {
                        font-size: 24px;
                    }

                    .wallet-name {
                        font-size: 18px;
                    }

                    .wallet-item {
                        padding: 20px 15px;
                    }

                    .step-title {
                        font-size: 12px;
                    }
                }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>'.$text['title'].'</h1>

                <div class="step-progress">
                    <div class="step active">
                        <div class="step-number">1</div>
                        <div class="step-title">'.$text['step1'].'</div>
                        <div class="step-line"></div>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-title">'.$text['step2'].'</div>
                        <div class="step-line"></div>
                    </div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-title">'.$text['step3'].'</div>
                    </div>
                </div>

                <div class="input-group">
                    <label>'.$text['amount'].'</label>
                    <input type="number" id="amount" placeholder="'.$text['amount_placeholder'].'" />
                </div>
                <div class="input-group">
                    <label>'.$text['payment_type'].'</label>
                    <select id="payment-type" onchange="showSelectedWallet()">
                        <option value="">'.$text['payment_type_placeholder'].'</option>';

        foreach($wallets as $wallet){
            $html .= '<option value="'.$wallet['id'].'">'.$wallet['name'].'</option>';
        }

        $html .= '</select>
                </div>

                <div id="wallets-container">';

        foreach($wallets as $wallet){
            $html .= '<div class="wallet-item" id="wallet-'.$wallet['id'].'">
                    <div class="wallet-name">'.$wallet['name'].'</div>
                    <div class="wallet-address">
                        <div class="address-container">
                            <div id="address-'.$wallet['id'].'">'.$wallet['account'].'</div>
                            <button class="copy-btn" onclick="copyAddress(\''.$wallet['account'].'\')">'.$text['copy_address'].'</button>
                        </div>
                    </div>
                    <div class="qrcode">
                        <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data='.urlencode($wallet['account']).'" />
                        <p style="margin-top:10px;color:#666;font-size:14px;">'.$text['scan_qrcode'].'</p>
                    </div>
                </div>';
        }

        $html .= '</div>

                <div class="upload-section">
                    <h3>'.$text['upload_title'].'</h3>
                    <div class="file-input-container">
                        <label for="screenshot" class="file-input-label">
                            <span>'.$text['select_file'].'</span>
                        </label>
                        <input type="file" id="screenshot" accept="image/*" />
                    </div>
                    <div id="preview" style="display:none;">
                        <img id="preview-image" />
                    </div>
                    <button id="submit-btn" class="submit-btn" onclick="submitPayment()">'.$text['submit'].'</button>
                </div>

                <a href="/index/my/index" class="back-btn">'.$text['back'].'</a>
            </div>

            <div id="toast" class="toast"></div>

            <script>
                // 定义语言文本变量
                var textMessages = {
                    copied: "'.$text['copied'].'",
                    amountError: "'.$text['amount_error'].'",
                    typeError: "'.$text['type_error'].'",
                    uploadError: "'.$text['upload_error'].'",
                    processing: "'.$text['processing'].'",
                    success: "'.$text['success'].'",
                    networkError: "'.$text['network_error'].'",
                    fail: "'.$text['fail'].'"
                };

                // 在页面加载完成后高亮第一个步骤
                document.addEventListener("DOMContentLoaded", function() {
                    updateSteps(1);
                });

                // 更新步骤状态
                function updateSteps(currentStep) {
                    var steps = document.querySelectorAll(".step");
                    for (var i = 0; i < steps.length; i++) {
                        if (i < currentStep) {
                            steps[i].classList.add("active");
                        } else {
                            steps[i].classList.remove("active");
                        }
                    }
                }

                function showSelectedWallet() {
                    // 隐藏所有钱包
                    var wallets = document.querySelectorAll(".wallet-item");
                    for(var i = 0; i < wallets.length; i++) {
                        wallets[i].style.display = "none";
                    }

                    // 显示选中的钱包
                    var selected = document.getElementById("payment-type").value;
                    if(selected) {
                        document.getElementById("wallet-" + selected).style.display = "block";
                        updateSteps(2); // 更新到第二步
                    }
                }

                function copyAddress(text) {
                    var input = document.createElement("input");
                    input.value = text;
                    document.body.appendChild(input);
                    input.select();
                    document.execCommand("copy");
                    document.body.removeChild(input);

                    // 显示提示信息
                    showToast(textMessages.copied);
                }

                function showToast(message) {
                    var toast = document.getElementById("toast");
                    toast.textContent = message;
                    toast.style.display = "block";

                    setTimeout(function() {
                        toast.style.display = "none";
                    }, 2000);
                }

                // 处理图片预览
                document.getElementById("screenshot").addEventListener("change", function() {
                    var file = this.files[0];
                    if (file) {
                        var reader = new FileReader();
                        reader.onload = function(e) {
                            document.getElementById("preview-image").src = e.target.result;
                            document.getElementById("preview").style.display = "block";
                            updateSteps(3); // 更新到第三步
                        }
                        reader.readAsDataURL(file);
                    }
                });

                // 提交充值
                function submitPayment() {
                    var amount = document.getElementById("amount").value;
                    var paymentType = document.getElementById("payment-type").value;
                    var screenshot = document.getElementById("screenshot").files[0];

                    if (!amount || amount <= 0) {
                        showToast(textMessages.amountError);
                        return;
                    }

                    if (!paymentType) {
                        showToast(textMessages.typeError);
                        return;
                    }

                    if (!screenshot) {
                        showToast(textMessages.uploadError);
                        return;
                    }

                    // 显示加载中状态
                    var submitBtn = document.getElementById("submit-btn");
                    var originalText = submitBtn.textContent;
                    submitBtn.textContent = textMessages.processing;
                    submitBtn.disabled = true;

                    var formData = new FormData();
                    formData.append("price", amount);
                    formData.append("type", "crypto");
                    formData.append("pic", screenshot);

                    // 获取选择的钱包地址
                    var selectedWalletAddress = document.getElementById("address-" + paymentType).innerText;
                    formData.append("address", selectedWalletAddress);

                    // 发送AJAX请求
                    var xhr = new XMLHttpRequest();
                    xhr.open("POST", "/index/ctrl/recharge_do", true);
                    xhr.onreadystatechange = function() {
                        if (xhr.readyState === 4) {
                            // 恢复按钮状态
                            submitBtn.textContent = originalText;
                            submitBtn.disabled = false;

                            if (xhr.status === 200) {
                                try {
                                    var response = JSON.parse(xhr.responseText);
                                    if (response.code === 0) {
                                        showToast(textMessages.success);
                                        setTimeout(function() {
                                            window.location.href = "/index/my/index";
                                        }, 1500);
                                    } else {
                                        showToast(response.info || textMessages.fail);
                                    }
                                } catch (e) {
                                    showToast(textMessages.fail);
                                }
                            } else {
                                showToast(textMessages.networkError);
                            }
                        }
                    };
                    xhr.send(formData);
                }
            </script>
        </body>
        </html>';

        return $html;
    }

    /**
     * 获取加密货币支付详情
     * 用于前端获取钱包地址，兼容旧的API调用
     */
    public function get_crypto_payment_details()
    {
        try {
            // 记录日志
            \think\facade\Log::record('Ctrl::get_crypto_payment_details被调用', 'info');

            // 获取参数
            $payment_method = input('post.payment_method/s', '');
            $amount = input('post.amount/f', 0);

            if (empty($payment_method) || $amount <= 0) {
                return json([
                    'status' => 'error',
                    'message' => '参数错误: 支付方式或金额无效',
                    'code' => 0,
                    'info' => '参数错误: 支付方式或金额无效',
                    'data' => []
                ]);
            }

            // 检查数据库字段
            try {
                $fields = \think\Db::query('SHOW COLUMNS FROM xy_bank');
                $has_bank_name = false;

                foreach ($fields as $field) {
                    if ($field['Field'] == 'bank_name') {
                        $has_bank_name = true;
                        break;
                    }
                }

                // 根据字段情况构建查询
                if ($has_bank_name) {
                    // 从数据库获取钱包地址
                    $wallet = Db::name('xy_bank')
                        ->where('status', 1)
                        ->where('bank_type', 'Crypto')
                        ->where('name', $payment_method)
                        ->field('id,name,bank_name,account,remarks')
                        ->find();
                } else {
                    // 从数据库获取钱包地址（不包含bank_name字段）
                    $wallet = Db::name('xy_bank')
                        ->where('status', 1)
                        ->where('bank_type', 'Crypto')
                        ->where('name', $payment_method)
                        ->field('id,name,account,remarks')
                        ->find();

                    // 如果找到了钱包，添加bank_name字段
                    if ($wallet) {
                        $wallet['bank_name'] = $wallet['name'];
                    }
                }
            } catch (\Exception $dbEx) {
                \think\facade\Log::error('数据库操作异常: ' . $dbEx->getMessage());
                $wallet = null;
            }

            // 如果数据库中没有，使用默认配置
            if (!$wallet) {
                // 默认钱包配置
                $default_wallets = [
                    'TRC20-usdt' => [
                        'address' => 'TRYZxfgFH5Z5FtZgcLCmfzSxMqgxZu2zJZ',
                        'name' => 'USDT (TRC20)'
                    ],
                    'ERC20-usdt' => [
                        'address' => '******************************************',
                        'name' => 'USDT (ERC20)'
                    ],
                    'BEP20-usdt' => [
                        'address' => '******************************************',
                        'name' => 'USDT (BEP20/BSC)'
                    ],
                    'BTC' => [
                        'address' => '******************************************',
                        'name' => 'Bitcoin (BTC)'
                    ]
                ];

                $payment_method_lower = strtolower($payment_method);
                $address = '';
                $method_name = $payment_method;

                foreach ($default_wallets as $key => $w) {
                    if (strtolower($key) === $payment_method_lower) {
                        $address = $w['address'];
                        $method_name = $w['name'];
                        break;
                    }
                }

                if (empty($address)) {
                    return json([
                        'status' => 'error',
                        'message' => '不支持的支付方式: ' . $payment_method,
                        'code' => 0,
                        'info' => '不支持的支付方式: ' . $payment_method,
                        'data' => []
                    ]);
                } else {
                    $wallet_data = [
                        'account' => $address,
                        'bank_name' => $method_name
                    ];
                }
            } else {
                $wallet_data = $wallet;
            }

            // 生成QR码URL
            $qr_code_url = "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=" . urlencode($wallet_data['account']);

            // 构建响应数据
            return json([
                'status' => 'success',
                'code' => 1,
                'info' => '获取成功',
                'data' => [
                    'method_name' => $wallet_data['bank_name'] ?? $wallet_data['name'] ?? $payment_method,
                    'address' => $wallet_data['account'],
                    'amount' => $amount,
                    'qr_code_url' => $qr_code_url,
                    'instructions' => '请向上述地址转账 ' . $amount . ' ' . strtoupper(str_replace('-', ' ', $payment_method))
                ]
            ]);
        } catch (\Exception $e) {
            // 记录错误，返回更详细的错误信息以便调试
            \think\facade\Log::error('获取支付详情失败: ' . $e->getMessage());

            return json([
                'status' => 'error',
                'message' => '系统错误，请稍后再试',
                'code' => 0,
                'info' => '系统错误，请稍后再试',
                'data' => []
            ]);
        }
    }

    /**
     * 充值记录页面
     */
    public function recharge_admin()
    {
        // 获取用户ID，优先使用session，其次使用cookie
        $uid = session('user_id');
        if (!$uid) {
            $uid = cookie('user_id');
        }

        // 如果仍然没有用户ID，重定向到登录页面
        if (!$uid) {
            return $this->redirect('User/login');
        }

        try {
            // 记录调试信息
            \think\facade\Log::info("充值记录查询开始，用户ID: {$uid}");
            
            // 先检查数据库连接和表是否存在
            $table_check = Db::query("SHOW TABLES LIKE 'xy_recharge'");
            if (empty($table_check)) {
                throw new \Exception("xy_recharge表不存在");
            }
            
            // 检查表中总记录数
            $total_all_records = Db::name('xy_recharge')->count();
            \think\facade\Log::info("xy_recharge表总记录数: {$total_all_records}");
            
            // 检查用户记录数
            $user_record_count = Db::name('xy_recharge')->where('uid', $uid)->count();
            \think\facade\Log::info("用户 {$uid} 的记录数: {$user_record_count}");
            
            // 简单的数据查询，参考Order.php的查询方式
            $list = db('xy_recharge')
                ->where('uid', $uid)
                ->order('addtime desc')
                ->paginate(10, false, [
                    'query' => request()->param()
                ]);

            // 处理数据显示
            $processedList = [];
            if ($list && $list->total() > 0) {
                foreach ($list->items() as $item) {
                    $processedItem = [
                        'id' => $item['id'],
                        'uid' => $item['uid'],
                        'num' => number_format($item['num'], 2),
                        'status' => $item['status'],
                        'addtime' => $item['addtime'],
                        'addtime_fmt' => date('Y-m-d H:i:s', $item['addtime']),
                        'pay_name' => isset($item['pay_name']) ? $item['pay_name'] : '',
                        'remark' => isset($item['remark']) ? $item['remark'] : ''
                    ];

                    // 状态文本
                    switch ($item['status']) {
                        case 1:
                            $processedItem['status_text'] = '<font color="#ff7070">' . lang('Pending') . '</font>';
                            break;
                        case 2:
                            $processedItem['status_text'] = '<font color="#777b9e">' . lang('examinationpassed') . '</font>';
                            break;
                        case 3:
                            $status_text = '<font color="#ff7070">' . lang('Auditfailure') . '</font>';
                            if (!empty($processedItem['remark'])) {
                                $status_text .= '(' . $processedItem['remark'] . ')';
                            }
                            $processedItem['status_text'] = $status_text;
                            break;
                        default:
                            $processedItem['status_text'] = '<font color="#999">未知状态</font>';
                            break;
                    }

                    $processedList[] = $processedItem;
                }
            }

            // 如果用户没有记录，但表中有其他记录，提供一些有用的信息
            $debug_info = '';
            if ($user_record_count == 0 && $total_all_records > 0) {
                // 获取一些其他用户的样本记录
                $sample_records = Db::name('xy_recharge')->limit(3)->select();
                $sample_uids = [];
                foreach ($sample_records as $record) {
                    $sample_uids[] = $record['uid'];
                }
                $debug_info = "表中有 {$total_all_records} 条记录，但用户 {$uid} 没有记录。其他用户ID示例: " . implode(', ', array_unique($sample_uids));
                \think\facade\Log::info($debug_info);
            }

            // 传递数据到视图
            $this->assign('list', $processedList);
            $this->assign('pagehtml', $list->render());
            $this->assign('title', lang('Rechargerecord'));
            $this->assign('total_count', $list->total());
            $this->assign('current_uid', $uid);
            $this->assign('debug_info', $debug_info);
            $this->assign('table_total', $total_all_records);

        } catch (\Exception $e) {
            // 错误处理
            \think\facade\Log::error('充值记录查询失败: ' . $e->getMessage());
            
            $this->assign('list', []);
            $this->assign('pagehtml', '');
            $this->assign('title', lang('Rechargerecord'));
            $this->assign('total_count', 0);
            $this->assign('current_uid', $uid);
            $this->assign('error_message', '数据加载失败: ' . $e->getMessage());
        }

        return $this->fetch();
    }

    public function deposit()
    {
        $uid = cookie('user_id');

        // 检查实名认证状态
        $user_info = Db::name('xy_users')->where('id', $uid)->find();
        if ($user_info['id_status'] != 1) {
            return $this->redirect('my/identity_verify');
        }

        $user = Db::name('xy_users')->where('id',$uid)->find();

        // 确保用户等级信息完整
        $level = isset($user['level']) ? $user['level'] : 0;

        // 获取用户等级信息
        $ulevel = Db::name('xy_level')->where('level',$level)->find();

        // 确保手续费为固定金额并设置到视图
        $this->assign('shouxu', isset($ulevel['tixian_shouxu']) ? $ulevel['tixian_shouxu'] : 0);
        $this->assign('user',$user);
        $info = db('xy_bankinfo')->where('uid',$uid)->where('status',1)->find();
        $this->assign('info',$info);

        return $this->fetch();
    }
}