<?php
echo "<h1>PHP环境测试</h1>";
echo "<p>PHP版本: " . PHP_VERSION . "</p>";
echo "<p>当前时间: " . date('Y-m-d H:i:s') . "</p>";

// 测试数据库连接
try {
    $config = include __DIR__ . '/config/database.php';
    $dsn = "mysql:host={$config['hostname']};port={$config['hostport']};dbname={$config['database']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password']);
    echo "<p style='color: green;'>✅ 数据库连接成功</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 数据库连接失败: " . $e->getMessage() . "</p>";
}

echo "<p><a href='/index/ctrl/vip'>测试VIP页面</a></p>";
?> 