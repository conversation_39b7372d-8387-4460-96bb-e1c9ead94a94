<?php
/**
 * 充值记录调试脚本
 * 用于诊断充值记录页面数据显示问题
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 引入ThinkPHP框架
require_once 'thinkphp/start.php';

use think\Db;

echo "<h1>充值记录调试</h1>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
.success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
.error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
.warning { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
.info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
table { border-collapse: collapse; width: 100%; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>";

try {
    // 1. 检查用户登录状态
    echo "<div class='debug-section info'>";
    echo "<h2>1. 用户登录状态检查</h2>";
    
    session_start();
    $uid_session = $_SESSION['user_id'] ?? null;
    $uid_cookie = $_COOKIE['user_id'] ?? null;
    
    echo "<p><strong>Session用户ID:</strong> " . ($uid_session ?: '未设置') . "</p>";
    echo "<p><strong>Cookie用户ID:</strong> " . ($uid_cookie ?: '未设置') . "</p>";
    
    $uid = $uid_session ?: $uid_cookie;
    echo "<p><strong>最终使用的用户ID:</strong> " . ($uid ?: '未获取到') . "</p>";
    
    if (!$uid) {
        echo "<p class='error'>❌ 用户未登录，这是导致没有记录的主要原因</p>";
        echo "</div>";
        
        echo "<div class='debug-section warning'>";
        echo "<h2>解决方案</h2>";
        echo "<p>请先登录系统，然后再访问充值记录页面。</p>";
        echo "<p><a href='/index/user/login'>点击这里登录</a></p>";
        echo "</div>";
        exit;
    }
    
    echo "<p class='success'>✅ 用户已登录</p>";
    echo "</div>";
    
    // 2. 检查用户是否存在
    echo "<div class='debug-section info'>";
    echo "<h2>2. 用户信息验证</h2>";
    
    $user = Db::name('xy_users')->where('id', $uid)->find();
    if ($user) {
        echo "<p class='success'>✅ 用户存在</p>";
        echo "<p><strong>用户名:</strong> " . $user['username'] . "</p>";
        echo "<p><strong>注册时间:</strong> " . date('Y-m-d H:i:s', $user['addtime']) . "</p>";
        echo "<p><strong>状态:</strong> " . ($user['status'] == 1 ? '正常' : '异常') . "</p>";
    } else {
        echo "<p class='error'>❌ 用户不存在</p>";
        echo "</div>";
        exit;
    }
    echo "</div>";
    
    // 3. 检查充值记录表结构
    echo "<div class='debug-section info'>";
    echo "<h2>3. 数据库表结构检查</h2>";
    
    try {
        $table_info = Db::query("SHOW COLUMNS FROM xy_recharge");
        echo "<p class='success'>✅ xy_recharge表存在</p>";
        echo "<table>";
        echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th></tr>";
        foreach ($table_info as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . ($column['Default'] ?: 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } catch (\Exception $e) {
        echo "<p class='error'>❌ 表结构检查失败: " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // 4. 检查充值记录数据
    echo "<div class='debug-section info'>";
    echo "<h2>4. 充值记录数据检查</h2>";
    
    // 检查该用户的所有充值记录
    $all_records = Db::name('xy_recharge')->where('uid', $uid)->select();
    echo "<p><strong>用户总充值记录数:</strong> " . count($all_records) . "</p>";
    
    if (count($all_records) > 0) {
        echo "<p class='success'>✅ 用户有充值记录</p>";
        echo "<table>";
        echo "<tr><th>ID</th><th>金额</th><th>状态</th><th>添加时间</th><th>支付方式</th><th>备注</th></tr>";
        foreach (array_slice($all_records, 0, 5) as $record) { // 只显示前5条
            echo "<tr>";
            echo "<td>" . $record['id'] . "</td>";
            echo "<td>" . $record['num'] . "</td>";
            echo "<td>" . $record['status'] . "</td>";
            echo "<td>" . date('Y-m-d H:i:s', $record['addtime']) . "</td>";
            echo "<td>" . ($record['pay_name'] ?: 'N/A') . "</td>";
            echo "<td>" . ($record['remark'] ?: 'N/A') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        if (count($all_records) > 5) {
            echo "<p>... 还有 " . (count($all_records) - 5) . " 条记录</p>";
        }
    } else {
        echo "<p class='warning'>⚠️ 用户没有充值记录</p>";
    }
    echo "</div>";
    
    // 5. 模拟控制器查询逻辑
    echo "<div class='debug-section info'>";
    echo "<h2>5. 模拟控制器查询</h2>";
    
    $where = [
        ['uid', '=', $uid]
    ];
    
    echo "<p><strong>查询条件:</strong></p>";
    echo "<pre>" . print_r($where, true) . "</pre>";
    
    try {
        $list = Db::name('xy_recharge')
            ->where($where)
            ->field('id, uid, num, status, addtime, pay_name, remark')
            ->order('addtime desc, id desc')
            ->limit(10)
            ->select();
            
        echo "<p><strong>查询结果数量:</strong> " . count($list) . "</p>";
        
        if (count($list) > 0) {
            echo "<p class='success'>✅ 查询成功，有数据</p>";
            echo "<table>";
            echo "<tr><th>ID</th><th>用户ID</th><th>金额</th><th>状态</th><th>时间</th></tr>";
            foreach ($list as $item) {
                echo "<tr>";
                echo "<td>" . $item['id'] . "</td>";
                echo "<td>" . $item['uid'] . "</td>";
                echo "<td>" . number_format($item['num'], 2) . "</td>";
                echo "<td>" . $item['status'] . "</td>";
                echo "<td>" . date('Y-m-d H:i:s', $item['addtime']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='error'>❌ 查询结果为空</p>";
        }
    } catch (\Exception $e) {
        echo "<p class='error'>❌ 查询失败: " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // 6. 检查分页查询
    echo "<div class='debug-section info'>";
    echo "<h2>6. 分页查询测试</h2>";
    
    try {
        $paginated_list = Db::name('xy_recharge')
            ->where($where)
            ->field('id, uid, num, status, addtime, pay_name, remark')
            ->order('addtime desc, id desc')
            ->paginate(10, false, [
                'query' => []
            ]);
            
        echo "<p><strong>分页对象类型:</strong> " . get_class($paginated_list) . "</p>";
        echo "<p><strong>分页数据数量:</strong> " . count($paginated_list) . "</p>";
        echo "<p><strong>总记录数:</strong> " . $paginated_list->total() . "</p>";
        echo "<p><strong>当前页:</strong> " . $paginated_list->currentPage() . "</p>";
        
        if (count($paginated_list) > 0) {
            echo "<p class='success'>✅ 分页查询成功</p>";
        } else {
            echo "<p class='error'>❌ 分页查询结果为空</p>";
        }
    } catch (\Exception $e) {
        echo "<p class='error'>❌ 分页查询失败: " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
} catch (\Exception $e) {
    echo "<div class='debug-section error'>";
    echo "<h2>调试过程中发生错误</h2>";
    echo "<p>错误信息: " . $e->getMessage() . "</p>";
    echo "<p>文件: " . $e->getFile() . "</p>";
    echo "<p>行号: " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<div class='debug-section warning'>";
echo "<h2>调试完成</h2>";
echo "<p>如果上述检查都正常，但页面仍然显示没有记录，可能的原因：</p>";
echo "<ul>";
echo "<li>模板文件中的变量名不匹配</li>";
echo "<li>控制器中的异常处理导致返回空数据</li>";
echo "<li>ThinkPHP的分页对象处理有问题</li>";
echo "<li>浏览器缓存问题</li>";
echo "</ul>";
echo "</div>";
?>
