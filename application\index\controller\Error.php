<?php
namespace app\index\controller;

use think\Controller;
use think\Request;
use think\Db;

/**
 * 错误处理控制器
 * 用于处理找不到的控制器和方法
 */
class Error extends Controller
{
    /**
     * 错误处理 - 一般方法
     */
    public function _empty($name)
    {
        // 记录错误日志
        \think\facade\Log::error('访问了不存在的控制器或方法: ' . $name . ' - URL: ' . request()->url());

        // 对特定方法进行重定向处理
        if ($name == 'recharge') {
            return $this->recharge();
        }

        // 检查是否是已知的控制器方法被错误路由到这里
        $known_methods = ['recharge_admin', 'junior', 'deposit_admin'];
        if (in_array($name, $known_methods)) {
            // 尝试重定向到正确的控制器
            try {
                $ctrl = new \app\index\controller\Ctrl();
                if (method_exists($ctrl, $name)) {
                    return $ctrl->$name();
                }
            } catch (\Exception $e) {
                \think\facade\Log::error('重定向到Ctrl控制器失败: ' . $e->getMessage());
            }
        }

        // 其他情况返回404错误页面
        return $this->fetch('public/error_404');
    }

    /**
     * 充值页面特殊处理
     * 重定向到加密货币充值控制器
     */
    public function recharge()
    {
        // 记录重定向日志
        \think\facade\Log::info('Error::recharge被调用，重定向到CryptoRecharge::recharge');

        // 实例化加密货币充值控制器并调用其recharge方法
        $controller = new CryptoRecharge();
        return $controller->recharge();
    }

    /**
     * 默认页面
     */
    public function index()
    {
        // 记录访问日志
        \think\facade\Log::error('Error控制器index方法被调用 - URL: ' . request()->url());

        // 检查是否是特定页面的错误访问
        $url = request()->url();
        if (strpos($url, 'recharge_admin') !== false) {
            // 尝试重定向到充值记录页面
            try {
                $ctrl = new \app\index\controller\Ctrl();
                return $ctrl->recharge_admin();
            } catch (\Exception $e) {
                \think\facade\Log::error('重定向到充值记录页面失败: ' . $e->getMessage());
            }
        } elseif (strpos($url, 'junior') !== false) {
            // 尝试重定向到团队页面
            try {
                $ctrl = new \app\index\controller\Ctrl();
                return $ctrl->junior();
            } catch (\Exception $e) {
                \think\facade\Log::error('重定向到团队页面失败: ' . $e->getMessage());
            }
        } elseif (strpos($url, 'deposit_admin') !== false) {
            // 尝试重定向到提现记录页面
            try {
                $ctrl = new \app\index\controller\Ctrl();
                return $ctrl->deposit_admin();
            } catch (\Exception $e) {
                \think\facade\Log::error('重定向到提现记录页面失败: ' . $e->getMessage());
            }
        }

        // 默认情况下显示404页面而不是重定向到首页
        return $this->fetch('public/error_404');
    }
}