<?php
/**
 * 路由测试脚本
 * 用于测试充值记录和团队报告页面的路由是否正常工作
 */

// 模拟用户登录状态
session_start();
$_SESSION['user_id'] = 1; // 假设用户ID为1
setcookie('user_id', '1', time() + 3600, '/');

echo "<h1>路由测试页面</h1>";

// 测试路由列表
$test_routes = [
    '/index/ctrl/recharge_admin' => '充值记录页面',
    '/index/ctrl/junior' => '团队报告页面',
    '/index/ctrl/deposit_admin' => '提现记录页面',
    '/index/my/index' => '用户中心页面'
];

echo "<h2>测试结果：</h2>";
echo "<ul>";

foreach ($test_routes as $route => $description) {
    echo "<li>";
    echo "<strong>{$description}</strong>: ";
    echo "<a href='{$route}' target='_blank'>{$route}</a>";
    echo "</li>";
}

echo "</ul>";

echo "<h2>当前环境信息：</h2>";
echo "<p>Session ID: " . session_id() . "</p>";
echo "<p>User ID (Session): " . ($_SESSION['user_id'] ?? 'Not set') . "</p>";
echo "<p>User ID (Cookie): " . ($_COOKIE['user_id'] ?? 'Not set') . "</p>";
echo "<p>当前时间: " . date('Y-m-d H:i:s') . "</p>";

echo "<h2>路由配置检查：</h2>";
echo "<p>请检查以下文件是否存在：</p>";
echo "<ul>";
echo "<li>route/route.php - 路由配置文件</li>";
echo "<li>application/index/controller/Ctrl.php - 控制器文件</li>";
echo "<li>application/index/view/ctrl/recharge_admin.html - 充值记录模板</li>";
echo "<li>application/index/view/ctrl/junior.html - 团队报告模板</li>";
echo "</ul>";
?>
