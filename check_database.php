<?php
/**
 * 数据库表结构检查脚本
 * 检查实际的数据库表结构和字段名
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 引入ThinkPHP框架
require_once 'thinkphp/start.php';

use think\Db;

echo "<h1>数据库表结构检查</h1>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
.success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
.error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
.warning { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
.info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
</style>";

try {
    // 1. 检查xy_recharge表结构
    echo "<div class='section info'>";
    echo "<h2>1. xy_recharge表结构检查</h2>";
    
    try {
        $recharge_columns = Db::query("SHOW COLUMNS FROM xy_recharge");
        echo "<p class='success'>✅ xy_recharge表存在</p>";
        echo "<table>";
        echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th><th>额外</th></tr>";
        foreach ($recharge_columns as $column) {
            echo "<tr>";
            echo "<td><strong>" . $column['Field'] . "</strong></td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . ($column['Default'] ?: 'NULL') . "</td>";
            echo "<td>" . ($column['Extra'] ?: '-') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // 检查是否有数据
        $recharge_count = Db::name('xy_recharge')->count();
        echo "<p><strong>总记录数:</strong> {$recharge_count}</p>";
        
        if ($recharge_count > 0) {
            $sample_data = Db::name('xy_recharge')->limit(3)->select();
            echo "<h3>样本数据:</h3>";
            echo "<table>";
            echo "<tr><th>ID</th><th>用户ID</th><th>金额</th><th>状态</th><th>时间</th></tr>";
            foreach ($sample_data as $row) {
                echo "<tr>";
                echo "<td>" . $row['id'] . "</td>";
                echo "<td>" . ($row['uid'] ?? $row['user_id'] ?? 'N/A') . "</td>";
                echo "<td>" . ($row['num'] ?? $row['amount'] ?? 'N/A') . "</td>";
                echo "<td>" . ($row['status'] ?? 'N/A') . "</td>";
                echo "<td>" . (isset($row['addtime']) ? date('Y-m-d H:i:s', $row['addtime']) : (isset($row['created_at']) ? $row['created_at'] : 'N/A')) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } catch (\Exception $e) {
        echo "<p class='error'>❌ xy_recharge表检查失败: " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // 2. 检查xy_users表结构
    echo "<div class='section info'>";
    echo "<h2>2. xy_users表结构检查</h2>";
    
    try {
        $users_columns = Db::query("SHOW COLUMNS FROM xy_users");
        echo "<p class='success'>✅ xy_users表存在</p>";
        echo "<table>";
        echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th><th>额外</th></tr>";
        foreach ($users_columns as $column) {
            echo "<tr>";
            echo "<td><strong>" . $column['Field'] . "</strong></td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . ($column['Default'] ?: 'NULL') . "</td>";
            echo "<td>" . ($column['Extra'] ?: '-') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // 检查关键字段
        $key_fields = ['pid', 'parent_id', 'referrer_id', 'invite_uid'];
        echo "<h3>关键字段检查:</h3>";
        foreach ($key_fields as $field) {
            $exists = false;
            foreach ($users_columns as $column) {
                if ($column['Field'] == $field) {
                    $exists = true;
                    break;
                }
            }
            $status = $exists ? "✅ 存在" : "❌ 不存在";
            echo "<p><strong>{$field}:</strong> {$status}</p>";
        }
        
        // 检查是否有用户数据
        $users_count = Db::name('xy_users')->count();
        echo "<p><strong>总用户数:</strong> {$users_count}</p>";
        
    } catch (\Exception $e) {
        echo "<p class='error'>❌ xy_users表检查失败: " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // 3. 检查当前登录用户
    echo "<div class='section info'>";
    echo "<h2>3. 当前登录用户检查</h2>";
    
    session_start();
    $uid_session = $_SESSION['user_id'] ?? null;
    $uid_cookie = $_COOKIE['user_id'] ?? null;
    
    echo "<p><strong>Session用户ID:</strong> " . ($uid_session ?: '未设置') . "</p>";
    echo "<p><strong>Cookie用户ID:</strong> " . ($uid_cookie ?: '未设置') . "</p>";
    
    $uid = $uid_session ?: $uid_cookie;
    
    if ($uid) {
        try {
            $user = Db::name('xy_users')->where('id', $uid)->find();
            if ($user) {
                echo "<p class='success'>✅ 用户存在</p>";
                echo "<p><strong>用户名:</strong> " . ($user['username'] ?? $user['name'] ?? 'N/A') . "</p>";
                echo "<p><strong>手机号:</strong> " . ($user['tel'] ?? $user['phone'] ?? 'N/A') . "</p>";
                echo "<p><strong>状态:</strong> " . ($user['status'] ?? 'N/A') . "</p>";
                
                // 检查该用户的充值记录
                $user_recharge_count = Db::name('xy_recharge')->where('uid', $uid)->count();
                echo "<p><strong>该用户充值记录数:</strong> {$user_recharge_count}</p>";
                
                if ($user_recharge_count > 0) {
                    $user_recharges = Db::name('xy_recharge')->where('uid', $uid)->limit(3)->select();
                    echo "<h3>该用户的充值记录:</h3>";
                    echo "<table>";
                    echo "<tr><th>ID</th><th>金额</th><th>状态</th><th>时间</th></tr>";
                    foreach ($user_recharges as $row) {
                        echo "<tr>";
                        echo "<td>" . $row['id'] . "</td>";
                        echo "<td>" . $row['num'] . "</td>";
                        echo "<td>" . $row['status'] . "</td>";
                        echo "<td>" . date('Y-m-d H:i:s', $row['addtime']) . "</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                }
                
            } else {
                echo "<p class='error'>❌ 用户不存在</p>";
            }
        } catch (\Exception $e) {
            echo "<p class='error'>❌ 用户查询失败: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p class='warning'>⚠️ 用户未登录</p>";
    }
    echo "</div>";
    
    // 4. 数据库连接测试
    echo "<div class='section info'>";
    echo "<h2>4. 数据库连接测试</h2>";
    
    try {
        $db_config = Db::getConfig();
        echo "<p class='success'>✅ 数据库连接正常</p>";
        echo "<p><strong>数据库类型:</strong> " . ($db_config['type'] ?? 'N/A') . "</p>";
        echo "<p><strong>数据库名:</strong> " . ($db_config['database'] ?? 'N/A') . "</p>";
        echo "<p><strong>主机:</strong> " . ($db_config['hostname'] ?? 'N/A') . "</p>";
        echo "<p><strong>端口:</strong> " . ($db_config['hostport'] ?? 'N/A') . "</p>";
    } catch (\Exception $e) {
        echo "<p class='error'>❌ 数据库连接失败: " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
} catch (\Exception $e) {
    echo "<div class='section error'>";
    echo "<h2>检查过程中发生错误</h2>";
    echo "<p>错误信息: " . $e->getMessage() . "</p>";
    echo "<p>文件: " . $e->getFile() . "</p>";
    echo "<p>行号: " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<div class='section warning'>";
echo "<h2>下一步修复建议</h2>";
echo "<p>根据上述检查结果，我将针对性地修复代码中的字段名和查询逻辑。</p>";
echo "</div>";
?>
