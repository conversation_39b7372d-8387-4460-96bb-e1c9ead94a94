<?php
/**
 * 路由测试页面
 * 直接测试路由是否正常工作
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>路由测试页面</h1>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.test-link { 
    display: block; 
    margin: 10px 0; 
    padding: 10px; 
    background: #f0f0f0; 
    border: 1px solid #ddd; 
    text-decoration: none; 
    color: #333;
    border-radius: 5px;
}
.test-link:hover { background: #e0e0e0; }
.success { color: green; }
.error { color: red; }
.info { color: blue; }
</style>";

// 测试路由列表
$test_routes = [
    '/index/ctrl/recharge_admin' => '充值记录页面',
    '/index/ctrl/junior' => '团队报告页面', 
    '/index/ctrl/deposit_admin' => '提现记录页面',
    '/ctrl/recharge_admin' => '充值记录页面（简化路径）',
    '/ctrl/junior' => '团队报告页面（简化路径）',
    '/ctrl/deposit_admin' => '提现记录页面（简化路径）'
];

echo "<h2>路由测试链接：</h2>";
echo "<p class='info'>请在登录状态下点击以下链接进行测试：</p>";

foreach ($test_routes as $route => $description) {
    echo "<a href='{$route}' class='test-link' target='_blank'>";
    echo "<strong>{$description}</strong><br>";
    echo "<small>路径: {$route}</small>";
    echo "</a>";
}

// 显示当前环境信息
echo "<h2>环境信息：</h2>";
echo "<p><strong>当前时间:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p><strong>服务器软件:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</p>";
echo "<p><strong>PHP版本:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>当前URL:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'Unknown') . "</p>";

// 检查关键文件
echo "<h2>文件检查：</h2>";
$files = [
    'route/route.php' => '路由配置文件',
    'application/index/controller/Ctrl.php' => '主控制器',
    'application/index/controller/Error.php' => '错误控制器'
];

foreach ($files as $file => $desc) {
    $exists = file_exists($file);
    $status = $exists ? "<span class='success'>✅ 存在</span>" : "<span class='error'>❌ 不存在</span>";
    echo "<p><strong>{$desc}:</strong> {$status}</p>";
}

// JavaScript测试
echo "<h2>JavaScript测试：</h2>";
echo "<button onclick='testRoute(\"/index/ctrl/recharge_admin\")'>测试充值记录路由</button>";
echo "<button onclick='testRoute(\"/index/ctrl/junior\")'>测试团队报告路由</button>";
echo "<div id='test-result'></div>";

echo "<script>
function testRoute(url) {
    document.getElementById('test-result').innerHTML = '<p class=\"info\">正在测试路由: ' + url + '</p>';
    
    fetch(url, {
        method: 'GET',
        credentials: 'same-origin'
    })
    .then(response => {
        if (response.ok) {
            document.getElementById('test-result').innerHTML = '<p class=\"success\">✅ 路由 ' + url + ' 响应正常 (状态码: ' + response.status + ')</p>';
        } else {
            document.getElementById('test-result').innerHTML = '<p class=\"error\">❌ 路由 ' + url + ' 响应异常 (状态码: ' + response.status + ')</p>';
        }
    })
    .catch(error => {
        document.getElementById('test-result').innerHTML = '<p class=\"error\">❌ 路由 ' + url + ' 请求失败: ' + error.message + '</p>';
    });
}
</script>";

echo "<h2>诊断建议：</h2>";
echo "<ol>";
echo "<li>确保已登录系统</li>";
echo "<li>检查浏览器控制台是否有JavaScript错误</li>";
echo "<li>检查服务器错误日志</li>";
echo "<li>清除浏览器缓存后重试</li>";
echo "<li>如果问题持续，请检查ThinkPHP的路由缓存</li>";
echo "</ol>";
?>
