<?php
/**
 * 深度路由诊断脚本
 * 用于诊断充值记录和团队报告页面跳转问题
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>路由诊断报告</h1>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
.success { background-color: #d4edda; border-color: #c3e6cb; }
.error { background-color: #f8d7da; border-color: #f5c6cb; }
.warning { background-color: #fff3cd; border-color: #ffeaa7; }
.info { background-color: #d1ecf1; border-color: #bee5eb; }
pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
</style>";

// 1. 检查文件存在性
echo "<div class='section info'>";
echo "<h2>1. 文件存在性检查</h2>";

$files_to_check = [
    'route/route.php' => '路由配置文件',
    'application/index/controller/Ctrl.php' => '主控制器文件',
    'application/index/controller/Base.php' => '基础控制器文件',
    'application/index/view/ctrl/recharge_admin.html' => '充值记录模板',
    'application/index/view/ctrl/junior.html' => '团队报告模板',
    'application/index/view/my/index.html' => '用户中心模板'
];

foreach ($files_to_check as $file => $description) {
    $exists = file_exists($file);
    $status = $exists ? "✅ 存在" : "❌ 不存在";
    echo "<p><strong>{$description}</strong>: {$status} ({$file})</p>";
}
echo "</div>";

// 2. 检查路由配置
echo "<div class='section info'>";
echo "<h2>2. 路由配置检查</h2>";

if (file_exists('route/route.php')) {
    $route_content = file_get_contents('route/route.php');
    
    // 检查关键路由
    $routes_to_check = [
        'index/ctrl/recharge_admin' => '充值记录路由',
        'index/ctrl/junior' => '团队报告路由',
        'index/ctrl/deposit_admin' => '提现记录路由'
    ];
    
    foreach ($routes_to_check as $route => $description) {
        $found = strpos($route_content, $route) !== false;
        $status = $found ? "✅ 已配置" : "❌ 未配置";
        echo "<p><strong>{$description}</strong>: {$status}</p>";
    }
    
    // 检查问题配置
    $problematic_configs = [
        'Route::miss' => '404重定向配置',
        "Route::rule('/', 'index/Ctrl/recharge')" => '根路径重定向配置'
    ];
    
    echo "<h3>问题配置检查:</h3>";
    foreach ($problematic_configs as $config => $description) {
        $found = strpos($route_content, $config) !== false;
        $commented = strpos($route_content, '//' . $config) !== false || strpos($route_content, '// ' . $config) !== false;
        
        if ($found && !$commented) {
            echo "<p class='error'><strong>{$description}</strong>: ❌ 存在且未注释 (可能导致问题)</p>";
        } elseif ($found && $commented) {
            echo "<p class='success'><strong>{$description}</strong>: ✅ 已注释 (已修复)</p>";
        } else {
            echo "<p class='success'><strong>{$description}</strong>: ✅ 不存在</p>";
        }
    }
} else {
    echo "<p class='error'>❌ 路由配置文件不存在</p>";
}
echo "</div>";

// 3. 检查控制器方法
echo "<div class='section info'>";
echo "<h2>3. 控制器方法检查</h2>";

if (file_exists('application/index/controller/Ctrl.php')) {
    $ctrl_content = file_get_contents('application/index/controller/Ctrl.php');
    
    $methods_to_check = [
        'public function recharge_admin(' => '充值记录方法',
        'public function junior(' => '团队报告方法',
        'public function deposit_admin(' => '提现记录方法'
    ];
    
    foreach ($methods_to_check as $method => $description) {
        $found = strpos($ctrl_content, $method) !== false;
        $status = $found ? "✅ 存在" : "❌ 不存在";
        echo "<p><strong>{$description}</strong>: {$status}</p>";
    }
} else {
    echo "<p class='error'>❌ 控制器文件不存在</p>";
}
echo "</div>";

// 4. 模拟请求测试
echo "<div class='section info'>";
echo "<h2>4. 模拟请求测试</h2>";

$test_urls = [
    '/index/ctrl/recharge_admin' => '充值记录页面',
    '/index/ctrl/junior' => '团队报告页面',
    '/index/ctrl/deposit_admin' => '提现记录页面'
];

foreach ($test_urls as $url => $description) {
    echo "<p><strong>{$description}</strong>: ";
    echo "<a href='{$url}' target='_blank' style='color: blue; text-decoration: underline;'>";
    echo "点击测试 {$url}</a></p>";
}
echo "</div>";

// 5. 环境信息
echo "<div class='section info'>";
echo "<h2>5. 环境信息</h2>";
echo "<p><strong>PHP版本</strong>: " . PHP_VERSION . "</p>";
echo "<p><strong>服务器软件</strong>: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</p>";
echo "<p><strong>文档根目录</strong>: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "</p>";
echo "<p><strong>当前脚本路径</strong>: " . __FILE__ . "</p>";
echo "<p><strong>当前工作目录</strong>: " . getcwd() . "</p>";
echo "</div>";

// 6. 建议的修复步骤
echo "<div class='section warning'>";
echo "<h2>6. 建议的修复步骤</h2>";
echo "<ol>";
echo "<li>确保所有必要的文件都存在</li>";
echo "<li>检查路由配置是否正确</li>";
echo "<li>确保控制器方法存在且可访问</li>";
echo "<li>检查用户认证逻辑</li>";
echo "<li>清除浏览器缓存并重新测试</li>";
echo "<li>检查服务器错误日志</li>";
echo "</ol>";
echo "</div>";

echo "<div class='section info'>";
echo "<h2>7. 快速测试链接</h2>";
echo "<p>请在登录状态下点击以下链接进行测试：</p>";
echo "<ul>";
echo "<li><a href='/index/ctrl/recharge_admin' target='_blank'>充值记录页面</a></li>";
echo "<li><a href='/index/ctrl/junior' target='_blank'>团队报告页面</a></li>";
echo "<li><a href='/index/ctrl/deposit_admin' target='_blank'>提现记录页面</a></li>";
echo "<li><a href='/index/my/index' target='_blank'>用户中心页面</a></li>";
echo "</ul>";
echo "</div>";
?>
